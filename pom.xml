<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.ijiami.manager</groupId>
		<artifactId>ijiami-manager</artifactId>
		<version>2.6.11-SNAPSHOT</version>
	</parent>
	<groupId>cn.ijiami.detection</groupId>
	<artifactId>privacy-android-server</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>privacy-android-server</name>
	<description>个人信息安全Android检测服务V1.0</description>
	<properties>
		<mysql8.version>8.4.0</mysql8.version>
		<ijiami-organ.version>2.6.11-SNAPSHOT</ijiami-organ.version>
		<ijiami-message.version>2.6.11-SNAPSHOT</ijiami-message.version>
		<ijiami-message-core.version>2.6.8-SNAPSHOT</ijiami-message-core.version>
		<ijiami-base.version>2.6.11-SNAPSHOT</ijiami-base.version>
		<ijiami-framework.version>2.6.11-SNAPSHOT</ijiami-framework.version>
		<ijiami.base.version>2.6.11-SNAPSHOT</ijiami.base.version>
		<ijiami-manager-security.version>2.6.11-SNAPSHOT</ijiami-manager-security.version>
		<ijiami.manager.ai.version>2.6.11-SNAPSHOT</ijiami.manager.ai.version>
		<spring-cloud.version>2021.0.5</spring-cloud.version>
		<spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<!-- Spring Cloud BOM -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- Spring Cloud Alibaba BOM -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- 项目内部模块版本管理 -->
			<dependency>
				<groupId>cn.ijiami.detection</groupId>
				<artifactId>privacy-android-server-client</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>cn.ijiami.detection</groupId>
				<artifactId>pinfo-detection-client</artifactId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		
		<!-- 国际化组件 -->
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-i18n</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-file</artifactId>
		</dependency>

		<dependency>
		  <groupId>cn.ijiami.organ</groupId>
		  <artifactId>ijiami-organ-service-impl</artifactId>
		   <version>${ijiami-organ.version}</version>
		</dependency>

		<dependency>
		   <groupId>cn.ijiami.organ</groupId>
		   <artifactId>ijiami-organ-rest</artifactId>
		    <version>${ijiami-organ.version}</version>
		</dependency>
		<dependency>
			<artifactId>spring-data-commons</artifactId>
			<groupId>org.springframework.data</groupId>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-utils</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.ijiami</groupId>
  					<artifactId>IJMSecurityDetection_IOS</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- json -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version></version>
		</dependency>
		
		<dependency>
			<groupId>com.alibaba</groupId>
   			<artifactId>fastjson</artifactId>
		</dependency>

		<!-- apche poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-collections4</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>4.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.xmlbeans</groupId>
					<artifactId>xmlbeans</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>3.1.0</version>
		</dependency>
		
		<dependency>
		  <groupId>cn.ijiami.framework</groupId>
		  <artifactId>ijiami-framework-mongodb</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>23.0</version>
		</dependency>
		
		<dependency>
		    <groupId>org.springframework.cloud</groupId>
		    <artifactId>spring-cloud-starter-openfeign</artifactId>
		    <version>2.1.3.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>${mysql8.version}</version>
		</dependency>
		
		<!-- 达梦数据库驱动 -->
		<dependency>
		    <groupId>com.dm</groupId>
		    <artifactId>DmJdbcDriver18</artifactId>
		    <version>1.8</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<modules>
		<module>privacy-android-server-client</module>
		<module>privacy-android-server-api</module>
		<module>privacy-android-server-impl</module>
		<module>privacy-android-server-startup</module>
	</modules>
</project>