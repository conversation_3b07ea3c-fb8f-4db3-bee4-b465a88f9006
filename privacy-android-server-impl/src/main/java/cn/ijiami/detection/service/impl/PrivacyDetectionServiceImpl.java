package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.PinfoConstant.BAISHENG_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.BASE_INFO_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.NEW_ANDROID_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.OLD_ANDROID_ITEMS;
import static cn.ijiami.detection.utils.CommonUtil.*;
import static cn.ijiami.detection.utils.ConstantsUtils.REPORT_NUM_5001;
import static cn.ijiami.detection.utils.ConstantsUtils.REPORT_NUM_5002;
import static cn.ijiami.detection.utils.ConstantsUtils.REPORT_NUM_6001;
import static cn.ijiami.detection.utils.ConstantsUtils.REPORT_NUM_6002;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.server.client.base.dto.CountLawDetectResultDTO;
import cn.ijiami.detection.server.client.base.dto.LawActionDetailDTO;
import cn.ijiami.detection.server.client.base.dto.LawDetectDetailDTO;
import cn.ijiami.detection.server.client.base.dto.LawDetectResultDTO;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.service.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.qcloud.cos.utils.Md5Utils;

import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.DTO.ReportShakeItemDTO;
import cn.ijiami.detection.VO.bigdata.BigDataConnectVO;
import cn.ijiami.detection.VO.bigdata.DetectionResultVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.IpDetailVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.detection.SensitiveWordFilePathVO;
import cn.ijiami.detection.VO.detection.TPrivacyPolicyTypeVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckAPIVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeAPIVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.VO.detection.statistical.DetectionChildResultDetailVO;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.bean.ShakeParam;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.server.client.base.enums.DetectionStatusEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.LawResultMarkStatusEnum;
import cn.ijiami.detection.server.client.base.enums.LawResultStatusEnum;
import cn.ijiami.detection.enums.LawResultTypeEnum;
import cn.ijiami.detection.enums.PermissionSensitiveTypeEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.ReportTypeEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TComplianceAppletPluginsMapper;
import cn.ijiami.detection.mapper.TDeepShakeValueMapper;
import cn.ijiami.detection.mapper.TDetectionItemMapper;
import cn.ijiami.detection.mapper.THardwareSensorShakeValueMapper;
import cn.ijiami.detection.mapper.TIndustryPermissionMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyCategoryMapper;
import cn.ijiami.detection.mapper.TPrivacyResultMarkMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TReportDesignMapper;
import cn.ijiami.detection.mapper.TReportStoreMapper;
import cn.ijiami.detection.mapper.TTaskAutoReportMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.query.ExcelReportQuery;
import cn.ijiami.detection.query.HardwareSensorShakeValueQuery;
import cn.ijiami.detection.query.ShakeValueQuery;
import cn.ijiami.detection.query.TaskReportDownLoadQuery;
import cn.ijiami.detection.service.IAppletService;
import cn.ijiami.detection.utils.ActionNougatUtils;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.CopyDirUtil;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.detection.utils.DateUtils;
import cn.ijiami.detection.utils.EngineCommandUtil;
import cn.ijiami.detection.utils.MapToJsonBatchtUtil;
import cn.ijiami.detection.utils.Text2Pic;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.detection.utils.WordPageUtils;
import cn.ijiami.detection.utils.WordStringUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.report.context.ReportContext;
import cn.ijiami.report.service.api.IReportChartService;
import cn.ijiami.report.service.api.IReportService;
import cn.ijiami.report.service.api.ReportManager;
import cn.ijiami.report.service.impl.DefaultReportManagerImpl;
import cn.ijiami.report.utils.PhantomJSUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2019-03-23 16:14
 */
@Slf4j
@Service
@CacheConfig(cacheNames = {"privacy-detection:taskDetail"})
public class PrivacyDetectionServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IPrivacyDetectionService {

    private static final Logger LOG = LoggerFactory.getLogger(IPrivacyDetectionService.class);

    public static final int TYPE_WORD = 1;

    private final TIndustryPermissionMapper industryPermissionMapper;
    private final TPrivacyCategoryMapper privacyCategoryMapper;
    private final TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    private final ITaskService taskService;
    private final TTaskMapper taskMapper;
    private final TAssetsMapper assetsMapper;
    private final ICommonMongodbService mongodbService;
    private final TPermissionMapper permissionMapper;
    private final ICommonMongodbService detectionMongodbService;
    private final TReportDesignMapper reportDesignMapper;
    private final IReportService reportService;
    private final IBaseFileService fileService;
    private final IReportChartService reportChartService;
    private final IjiamiCommonProperties commonProperties;
    private final IPrivacyActionService privacyActionService;
    private final IPrivacyActionNougatService privacyActionNougatService;
    private final IPrivacyPolicyImgService privacyPolicyImgService;
    private final IPrivacySensitiveWordService privacySensitiveWordService;
    private final IPrivacyOutsideAddressService privacyOutsideAddressService;
    private final IPrivacyCheckService          privacyCheckService;
    private final TDetectionItemMapper          detectionItemMapper;
    private final IPrivacySharedPrefsService    privacySharedPrefsService;
    private final IMiitDetectService            miitDetectService;
    private final TPrivacyResultMarkMapper      privacyResultMarkMapper;
    private final IapkCategoryService apkCategoryService;
    private final TTaskExtendMapper taskExtendMapper;
    private final TReportStoreMapper reportStoreMapper;
    private final IExcelReportService excelReportService;
    private final TPrivacyActionNougatMapper privacyActionNougatMapper;
    private final IStorageLogService storageLogService;

    private final TDeepShakeValueMapper deepShakeValueMapper;
    private final TComplianceAppletPluginsMapper complianceAppletPluginsMapper;
    private final IAddWatermarkService addWatermarkService;
    private final IAppletService appletService;

    private final THardwareSensorShakeValueMapper hardwareSensorShakeValueMapper;

    private final ITaskDataService taskDataService;

    private final TTaskAutoReportMapper taskAutoReportMapper;

    private final ITaskReportService taskReportService;

    @Value("${ijiami.logCrtl.config.path}")
    private String logCrtlConfigPath;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Value("${fastDFS.intranet.ip}")
    private String fastDFSIntranetIp;
    
    
    // 动态检测项
    private static String[] dynamic_items = new String[]{"0402", "0403", "0607", "0316", "0317", "0318"};
    // mongodb 查询返回字段
    private static String[] fields =
            new String[]{"_id", "assets_id", "template_id", "apk_name", "apk_version", "apk_package", "apk_logo", "md5", "sign_md5", "apk_size",
                    "apk_file_address", "apk_detection_score", "apk_highrisk_count", "apk_middlerisk_count", "apk_lowrisk_count", "apk_is_reinforce", "isSafe",
                    "apk_detection_starttime", "apk_detection_status", "describe", "apk_type", "is_delete", "terminal_type", "create_user_id", "create_time",
                    "progress", "update_time", "apk_detection_endtime", "apk_detection_time", "detection_version"};

    private final List<Long> ILLEGAL_COLLECTION_RISK_IDS = Arrays.asList(6L, 125L, 369L, 503L);
    private final List<Long> OUTSIDE_COLLECTION_RISK_IDS = Arrays.asList(7L, 126L, 370L, 504L);
    private final List<Long> ILLEGAL_USE_RISK_IDS = Arrays.asList(8L, 127L, 371L, 505L);
    private final List<Long> USE_PUSH_RISK_IDS = Arrays.asList(9L, 128L, 372L, 506L);
    private final List<Long> EXCESSIVE_ACCESS_RISK_IDS = Arrays.asList(10L, 129L, 373L, 507L);
    private final List<Long> ASSOCIATION_START_RISK_IDS = Arrays.asList(11L, 130L, 374L, 508L);

    @Autowired
    public PrivacyDetectionServiceImpl(TIndustryPermissionMapper industryPermissionMapper, TPrivacyCategoryMapper privacyCategoryMapper,
                                       TPrivacySharedPrefsMapper privacySharedPrefsMapper, TPermissionMapper permissionMapper, ITaskService taskService,
                                       TTaskMapper taskMapper, TAssetsMapper assetsMapper, ICommonMongodbService mongodbService,
                                       ICommonMongodbService detectionMongodbService, TReportDesignMapper reportDesignMapper, IReportService reportService,
                                       IBaseFileService fileService, IReportChartService reportChartService, IjiamiCommonProperties commonProperties,
                                       IPrivacyActionService privacyActionService, IPrivacyActionNougatService privacyActionNougatService,
                                       IPrivacyPolicyImgService privacyPolicyImgService, IPrivacySensitiveWordService privacySensitiveWordService,
                                       IPrivacyOutsideAddressService privacyOutsideAddressService, IPrivacyCheckService privacyCheckService,
                                       TDetectionItemMapper detectionItemMapper, IPrivacySharedPrefsService privacySharedPrefsService,
                                       IPrivacyDetectionTransferRiskService iPrivacyDetectionTransferRiskService,
                                       SingleFastDfsFileService singleFastDfsFileService, IMiitDetectService miitDetectService,
                                       TPrivacyResultMarkMapper privacyResultMarkMapper, IapkCategoryService apkCategoryService,
                                       TTaskExtendMapper taskExtendMapper, TReportStoreMapper reportStoreMapper, IExcelReportService excelReportService,
                                       TPrivacyActionNougatMapper privacyActionNougatMapper, IStorageLogService storageLogService,
                                       TDeepShakeValueMapper deepShakeValueMapper, TComplianceAppletPluginsMapper complianceAppletPluginsMapper,
                                       IAddWatermarkService addWatermarkService, IAppletService appletService,
                                       THardwareSensorShakeValueMapper hardwareSensorShakeValueMapper, ITaskDataService taskDataService,
                                       TTaskAutoReportMapper taskAutoReportMapper, ITaskReportService taskReportService) {
        this.industryPermissionMapper = industryPermissionMapper;
        this.privacyCategoryMapper = privacyCategoryMapper;
        this.privacySharedPrefsMapper = privacySharedPrefsMapper;
        this.taskService = taskService;
        this.taskMapper = taskMapper;
        this.assetsMapper = assetsMapper;
        this.mongodbService = mongodbService;
        this.permissionMapper = permissionMapper;
        this.detectionMongodbService = detectionMongodbService;
        this.reportDesignMapper = reportDesignMapper;
        this.reportService = reportService;
        this.fileService = fileService;
        this.reportChartService = reportChartService;
        this.commonProperties = commonProperties;
        this.privacyActionService = privacyActionService;
        this.privacyActionNougatService = privacyActionNougatService;
        this.privacyPolicyImgService = privacyPolicyImgService;
        this.privacySensitiveWordService = privacySensitiveWordService;
        this.privacyOutsideAddressService = privacyOutsideAddressService;
        this.privacyCheckService = privacyCheckService;
        this.detectionItemMapper = detectionItemMapper;
        this.privacySharedPrefsService = privacySharedPrefsService;
        this.iPrivacyDetectionTransferRiskService = iPrivacyDetectionTransferRiskService;
        this.singleFastDfsFileService = singleFastDfsFileService;
        this.miitDetectService = miitDetectService;
        this.privacyResultMarkMapper = privacyResultMarkMapper;
        this.apkCategoryService = apkCategoryService;
        this.taskExtendMapper = taskExtendMapper;
        this.reportStoreMapper = reportStoreMapper;
        this.excelReportService = excelReportService;
        this.privacyActionNougatMapper = privacyActionNougatMapper;
        this.storageLogService = storageLogService;
        this.deepShakeValueMapper = deepShakeValueMapper;
        this.complianceAppletPluginsMapper = complianceAppletPluginsMapper;
        this.addWatermarkService = addWatermarkService;
        this.appletService = appletService;
        this.hardwareSensorShakeValueMapper = hardwareSensorShakeValueMapper;
        this.taskDataService = taskDataService;
        this.taskAutoReportMapper = taskAutoReportMapper;
        this.taskReportService = taskReportService;
    }

    @Override
    public BaseMessageVO getAppBaseInfo(String documentId) throws IjiamiApplicationException {
        return taskService.getBaseMessage(documentId);
    }

    @Override
    public TaskDetailVO getTaskDetailVO(String documentId) {
        return findById(documentId);
    }

    @Override
    public List<SdkVO> getSDKList(String documentId) throws IjiamiApplicationException {
        Long taskId = taskMapper.findTaskIdByDocumentId(documentId);
        return taskService.getSdkList(documentId, taskId);
    }

    @Override
    public List<SdkVO> getSuspiciousSdkList(String documentId) throws IjiamiApplicationException {
        Long taskId = taskMapper.findTaskIdByDocumentId(documentId);
        return taskService.getSuspiciousSdkList(taskId);
    }


    @Override
    public SdkResponseVO getIosSDKList(String documentId) throws IjiamiApplicationException {
        Long taskId = taskMapper.findTaskIdByDocumentId(documentId);
        return taskService.getIosSDKList(documentId, taskId);
    }

    @Override
    public List<TComplianceAppletPlugins> getPluginList(String documentId) throws IjiamiApplicationException {
        Long taskId = taskMapper.findTaskIdByDocumentId(documentId);
        return appletService.findAppletPlugins(taskId);
    }

    @Override
    public List<SdkVO> getSDKList(String documentId, Long taskId) throws IjiamiApplicationException {
        return taskService.getSdkList(documentId, taskId);
    }

    private PermissionVO getPermission(List<PermissionVO> permissions, String name) {
        Optional<PermissionVO> optional = permissions.stream().filter(p -> name.equals(p.getName())).findFirst();
        return optional.orElse(null);
    }

    @Override
    public List<PermissionVO> getXMLPermission(String documentId, Integer behaviorStage) {
        TTask tTask = taskMapper.findByDocumentId(documentId);
        return getXMLPermission(tTask, behaviorStage);
    }

    @Override
    public List<PermissionVO> getXMLPermission(TTask tTask, Integer behaviorStage) {
        List<String> detectionPermissions = getDetectionPermissions(tTask.getApkDetectionDetailId());
        List<PermissionVO> xmlPermissions = new ArrayList<>();
        List<PermissionVO> permissionVOS = getPermissionVOS(xmlPermissions, detectionPermissions, tTask.getTerminalType().getValue());
        List<PermissionVO> actionPermissions = privacyActionNougatService.getActionPermissions(tTask.getTaskId(), behaviorStage);
        for (PermissionVO permissionVO : permissionVOS) {
            Optional<PermissionVO> optional = actionPermissions.stream().filter(ap -> ap.getName().equals(permissionVO.getName())).findFirst();
            optional.ifPresent(vo ->{
                permissionVO.setUseCount(vo.getUseCount());
                if(vo.getUseCount()>0){
                    permissionVO.setUsed(true);
                }
            });
            optional.ifPresent(vo -> permissionVO.setAppCount(vo.getAppCount()));
            optional.ifPresent(vo -> permissionVO.setSdkCount(vo.getSdkCount()));
            optional.ifPresent(vo -> permissionVO.setIsPrivacy(vo.getIsPrivacy()));

            optional.ifPresent(vo -> permissionVO.setAppB1Count(vo.getAppB1Count()));
            optional.ifPresent(vo -> permissionVO.setAppB2Count(vo.getAppB2Count()));
            optional.ifPresent(vo -> permissionVO.setAppB3Count(vo.getAppB3Count()));
            optional.ifPresent(vo -> permissionVO.setAppB4Count(vo.getAppB4Count()));

            optional.ifPresent(vo -> permissionVO.setSdkB1Count(vo.getSdkB1Count()));
            optional.ifPresent(vo -> permissionVO.setSdkB2Count(vo.getSdkB2Count()));
            optional.ifPresent(vo -> permissionVO.setSdkB3Count(vo.getSdkB3Count()));
            optional.ifPresent(vo -> permissionVO.setSdkB4Count(vo.getSdkB4Count()));
            optional.ifPresent(vo -> permissionVO.setAppB5Count(vo.getAppB5Count()));
            optional.ifPresent(vo -> permissionVO.setSdkB5Count(vo.getSdkB5Count()));
            permissionVO.setStatementPermission(true);
        }
        Collections.sort(permissionVOS);
        return permissionVOS;
    }

    @Override
    public List<PermissionVO> getSensitivePermission(String documentId, Integer behaviorStage) {
        TTask task = taskMapper.findByDocumentId(documentId);
        return getSensitivePermission(task, behaviorStage);
    }

    @Override
    public List<PermissionVO> getSensitivePermission(TTask task, Integer behaviorStage) {
        List<PermissionVO> sensitivePermission = new ArrayList<>();
        List<PermissionVO> xmlPermission = getXMLPermission(task, behaviorStage);
        List<PermissionVO> noDeclaredPermission = getNoDeclaredPermission(task, behaviorStage);
        sensitivePermission.addAll(xmlPermission.stream()
                .filter(permissionVO -> permissionVO.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value)
                .collect(Collectors.toList()));
        sensitivePermission.addAll(noDeclaredPermission.stream()
                .filter(permissionVO -> permissionVO.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value)
                .collect(Collectors.toList()));
        Collections.sort(sensitivePermission);
        return sensitivePermission;
    }

    @Override
    public List<PermissionVO> getNoDeclaredPermission(String documentId, Integer behaviorStage) {
        TTask task = taskMapper.findByDocumentId(documentId);
        return getNoDeclaredPermission(task, behaviorStage);
    }

    @Override
    public List<PermissionVO> getNoDeclaredPermission(TTask task, Integer behaviorStage) {
        List<PermissionVO> noDeclaredPermission = new ArrayList<>();
        List<PermissionVO> actionPermissions = privacyActionNougatService.getActionPermissions(task.getTaskId(), behaviorStage);
        List<String> xmlPermission = getDetectionPermissions(task.getApkDetectionDetailId());
        for (PermissionVO actionPermission : actionPermissions) {
            if(actionPermission.getUseCount()>0){
                actionPermission.setUsed(true);
            }
            if (!xmlPermission.contains(actionPermission.getName())) {
                noDeclaredPermission.add(actionPermission);
            }
        }
        Collections.sort(noDeclaredPermission);
        return noDeclaredPermission;
    }

    private List<PermissionVO> getAllPermission(Integer terminalType) {
        return permissionMapper.findAll(terminalType);
    }

    @Override
    public List<PermissionVO> getExcessPermission(String documentId, Integer behaviorStage) {
        TTask task = taskMapper.findByDocumentId(documentId);
        return getExcessPermission(task, behaviorStage);
    }

    @Override
    public List<PermissionVO> getExcessPermission(Long taskId, Integer behaviorStage) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        return getExcessPermission(task, behaviorStage);
    }

    @Override
    public List<PermissionVO> getExcessPermission(TTask task, Integer behaviorStage) {
        List<PermissionVO> excessPermissions = new ArrayList<>();
        List<PermissionVO> actionPermissions = privacyActionNougatService.getActionPermissions(task.getTaskId(), behaviorStage);
        List<String> minPermissions = new ArrayList<>();
        for (PermissionVO industryPermission : actionPermissions) {
            minPermissions.add(industryPermission.getName());
        }
        List<PermissionVO> xmlPermission = this.getXMLPermission(task, behaviorStage);
        for (PermissionVO permissionVO : xmlPermission) {
            if (!minPermissions.contains(permissionVO.getName()) && permissionVO.getIsPrivacy() == PrivacyStatusEnum.YES.getValue()) {
                permissionVO.setExcessPermission(true);
                excessPermissions.add(permissionVO);
            }
        }
        Collections.sort(excessPermissions);
        return excessPermissions;
    }

    @Override
    public List<PermissionVO> getPermissionVOS(List<PermissionVO> excessPermissions, List<String> xmlPermissions, Integer terminalType) {
        List<PermissionVO> permissions = getAllPermission(terminalType);
        for (String detectionPermission : xmlPermissions) {
            PermissionVO permission = getPermission(permissions, detectionPermission);
            PermissionVO permissionVO = new PermissionVO();
            if (permission != null) {
                BeanUtils.copyProperties(permission, permissionVO);
            } else {
                permissionVO.setName(detectionPermission);
                permissionVO.setRemark("自定义权限");
                permissionVO.setHarm(detectionPermission);
                permissionVO.setGrade(3);
                permissionVO.setType(1);
                permissionVO.setIsPrivacy(0);
            }
            excessPermissions.add(permissionVO);
        }
        Collections.sort(excessPermissions);
        return excessPermissions;
    }

    /**
     * html报告->权限使用情况
     *
     * @param task
     * @return
     */
    private List<PermissionVO> countPermissionUsed(TTask task) {
        List<PermissionVO> xmlPermission = getXMLPermission(task.getApkDetectionDetailId(), null);
        List<PermissionVO> permissionVOS = privacyActionNougatService.countActionPermissions(task.getTaskId());
        for (PermissionVO permissionVO : xmlPermission) {
            Optional<PermissionVO> optional =
                    permissionVOS.stream().filter(ap -> ap.getName().equals(permissionVO.getName()) && ap.getExecutorType() == 1).findFirst();
            optional.ifPresent(vo -> permissionVO.setAppCount(vo.getUseCount()));

            Optional<PermissionVO> optional1 =
                    permissionVOS.stream().filter(ap -> ap.getName().equals(permissionVO.getName()) && ap.getExecutorType() == 2).findFirst();
            optional1.ifPresent(vo -> permissionVO.setSdkCount(vo.getUseCount()));
        }
        return xmlPermission;
    }

    private HtmlReportCommonVO countPermissionActionIp(TTask task, List<PrivacyActionNougatVO> actionNougats, List<TPrivacyOutsideAddress> outSideAddress) {
        HtmlReportCommonVO vo = new HtmlReportCommonVO();
        vo.setActionCount(CollectionUtils.isEmpty(actionNougats) ? 0 : actionNougats.size());
        vo.setSensitiveAction(CollectionUtils.isEmpty(actionNougats) ? 0 : (int) actionNougats.stream().filter(a -> a.getSensitive() == 1).count());

        vo.setIpCount(CollectionUtils.isEmpty(outSideAddress) ? 0 : outSideAddress.size());
        vo.setInsideIp(CollectionUtils.isEmpty(outSideAddress) ? 0 : (int) outSideAddress.stream().filter(a -> a.getOutside() == PrivacyStatusEnum.NO.getValue()).count());
        vo.setOutsideIp(CollectionUtils.isEmpty(outSideAddress) ? 0 : (int) outSideAddress.stream().filter(a -> a.getOutside() == PrivacyStatusEnum.YES.getValue()).count());

        List<PermissionVO> sensitivePermission = getSensitivePermission(task.getApkDetectionDetailId(), null);
        vo.setSensitivePermission(CollectionUtils.isEmpty(sensitivePermission) ? 0 : sensitivePermission.size());
        List<PermissionVO> xmlPermission = getXMLPermission(task.getApkDetectionDetailId(), null);
        vo.setXmlPermission(CollectionUtils.isEmpty(xmlPermission) ? 0 : xmlPermission.size());
        List<PermissionVO> noDeclaredPermission = getNoDeclaredPermission(task.getApkDetectionDetailId(), null);
        vo.setNotDeclaredPermission(CollectionUtils.isEmpty(noDeclaredPermission) ? 0 : noDeclaredPermission.size());
        vo.setNotUsedPermission(CollectionUtils.isEmpty(xmlPermission) ? 0 : (int) xmlPermission.stream().filter(p -> p.getUseCount() == 0).count());
        return vo;
    }

    @Override
    public List<AppStorePrivacyApiVO> getAppStorePrivacyApiList(TTask task) {
        List<AppStorePrivacyApiVO> apiVOList = privacyActionNougatService.getAppStorePrivacyApiList(task.getTaskId());
        Optional<TPrivacyPolicyResult> privacyPolicy = iPrivacyDetectionTransferRiskService.privacyPolicyDetail(task.getTaskId());
        return apiVOList.stream().peek(apiVO -> {
            apiVO.setIsPolicyPrivacy(privacyPolicy.isPresent() && StringUtils.containsIgnoreCase(privacyPolicy.get().getDetailResult(), apiVO.getApiName()));
        }).collect(Collectors.toList());
    }

    @Override
    public ReportResultVO downloadReport(String documentId, Integer type, Integer reportType, Integer reportObject, Long userId, Integer terminalType) {
    	 return downloadReport(documentId, type, reportType, reportObject, userId, terminalType, null);
    }

    @Override
    public ReportResultVO downloadReport(String documentId, Integer type, Integer reportType, Integer reportObject, Long userId, Integer terminalType, String[] itemNo) {
        // 查询模板
        TReportDesign reportDesign = reportDesignMapper.findByUserIdAndReportObject(userId, reportObject, terminalType);
        if (reportDesign == null) {
            reportDesign = reportDesignMapper.findByUserIdAndReportObject(null, reportObject, terminalType);
        }
        long time1 = System.currentTimeMillis();
        String templateName = reportDesign.getTemplatePath().substring(reportDesign.getTemplatePath().lastIndexOf("/") + 1);
        
        TaskDetailVO taskDetailVO = detectionMongodbService.findDetectionResultExcludeField(documentId, fields);
        long time2 = System.currentTimeMillis();
        LOG.info("生成报告1.耗时:{}", (time2-time1));
        try{
        	cn.ijiami.base.common.file.entity.File logoFile = fileService.findFileByFileKey(reportDesign.getReportLogo());
            reportDesign.setReportLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + logoFile.getFilePath()));
            cn.ijiami.base.common.file.entity.File companyLogoFile = fileService.findFileByFileKey(reportDesign.getCompanyLogo());
            reportDesign.setCompanyLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + companyLogoFile.getFilePath()));
            cn.ijiami.base.common.file.entity.File headerLogoFile = fileService.findFileByFileKey(reportDesign.getHeaderLogo());
            reportDesign.setHeaderLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + headerLogoFile.getFilePath()));
            cn.ijiami.base.common.file.entity.File watermarkLogoFile = fileService.findFileByFileKey(reportDesign.getWatermarkLogo());
            reportDesign.setWatermarkLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + watermarkLogoFile.getFilePath()));
        }catch(Exception e){
            e.getMessage();
        }
        
        // 报告组装数据
        Map<String, Object> reportData = buildReportData(documentId, type, itemNo);
        reportData.put("reportDesign", reportDesign);
        TPrivacyPolicyType lawType = (TPrivacyPolicyType) reportData.get("law");
        long time3 = System.currentTimeMillis();
        Map<String, Object> map = toReportWordData(reportData);
        // 报告名称
        String reportOutFileName = generateReportName(reportObject, taskDetailVO, lawType);
        String fileName = generateReport(map, templateName, reportOutFileName, reportType);
        String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        File file = new File(reportRootPath + "out" + File.separator + fileName);
        if (file.exists()) {
            if (reportType == ReportTypeEnum.WORD.getValue()) {
                String pageUrl = commonProperties.getProperty("ijiami.report.page.url");
                if (StringUtils.isNotBlank(pageUrl)) {
                    WordPageUtils.urlPost(pageUrl, file.getAbsolutePath(), file.getAbsolutePath());
                }
            }
            long time4 = System.currentTimeMillis();
            LOG.info("生成报告.转换.耗时:{}", (time4-time3));
            return new ReportResultVO().appName(taskDetailVO.getApk_name())
                    .versionCode(taskDetailVO.getApk_version())
                    .terminalType(TerminalTypeEnum.getAndValid(terminalType)).report(file);
        }
        return null;
    }

    /**
     * 报告名称生成器
     *
     * @param reportObject      报告类型
     * @param taskDetailVO      任务详情
     * @param privacyPolicyType 法规信息
     * @return 报告名
     */
    private String generateReportName(Integer reportObject, TaskDetailVO taskDetailVO, TPrivacyPolicyType privacyPolicyType) {
        // 报告对象
        String lawName;
        if (Objects.nonNull(privacyPolicyType)) {
            // 处理特殊法规名称造成的报告下载异常
            lawName = privacyPolicyType.getLawName().replace("/", "_");
        } else {
            lawName = "";
        }
        return filenameFilter(CommonUtil.reportDocName(taskDetailVO, lawName)).replaceAll(" ","-");
    }

    @Override
    public File downloadZip(String documentId) {
        TaskDetailVO taskDetailVO = findById(documentId);
        TTask task = taskMapper.findByDocumentId(documentId);

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String newFileName = commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getApk_name() + "_" + format.format(taskDetailVO.getApk_detection_starttime());
        File zipDir = new File(newFileName);
        File zip = new File(zipDir.getAbsolutePath() + ".zip");
        boolean path = false;
        try {
            if (task.getTerminalType().getValue() == TerminalTypeEnum.ANDROID.getValue()) {
                if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
                    File autoZip = new File(
                            commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5() + "_" + task.getTaskId() + "_AUTO" + ".zip");
                    if (autoZip.exists()) {
                        FileUtils.copyFileToDirectory(autoZip, zipDir);
                        path = true;
                    } else {
                        List<TTaskData> taskDataList = taskDataService.findTaskData(task.getTaskId());
                        if (StringUtils.isNoneBlank(task.getDataPath()) && task.getDataPath().contains("group")) {
                            FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIp)), autoZip);
                        } else if(StringUtils.isBlank(task.getDataPath()) && !taskDataList.isEmpty()) {
                            zipStagedTaskFile(task.getTaskId(), taskDetailVO.getMd5(), zipDir, taskDataList);
                            path = true;
                        }
                    }
                }
                if (task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
                    File manualZip = new File(
                            commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5() + "_" + task.getTaskId() + "_MANUAL" + ".zip");
                    if (!manualZip.exists() && StringUtils.isNoneBlank(task.getDataPath()) && task.getDataPath().contains("group")) {
                        FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIp)), manualZip);
                    }
                    if (manualZip.exists()) {
                        FileUtils.copyFileToDirectory(manualZip, zipDir);
                        path = true;
                    }

                    File law1Zip = new File(
                            commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5() + "_" + task.getTaskId() + "_LAW_1" + ".zip");
                    if (law1Zip.exists()) {
                        FileUtils.copyFileToDirectory(law1Zip, zipDir);
                        path = true;
                    }

                    File law2Zip = new File(
                            commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5() + "_" + task.getTaskId() + "_LAW_2" + ".zip");
                    if (law2Zip.exists()) {
                        FileUtils.copyFileToDirectory(law2Zip, zipDir);
                        path = true;
                    }

                    File law3Zip = new File(
                            commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5() + "_" + task.getTaskId() + "_LAW_3" + ".zip");
                    if (law3Zip.exists()) {
                        FileUtils.copyFileToDirectory(law3Zip, zipDir);
                        path = true;
                    }
                }
            } else {
                String suffix = TaskDetectionTypeEnum.isAuto(task.getDetectionType()) ? "_AUTO" : "_MANUAL";
                File autoZip = new File(
                        commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5() + "_" + task.getTaskId() + suffix + ".tar.gz");
                if (!autoZip.exists() && StringUtils.isNoneBlank(task.getDataPath()) && task.getDataPath().contains("group")) {
                    LOG.info("下载数据包 {}", task.getDataPath());
                    FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIp)), autoZip);
                }
                if (autoZip.exists()) {
                    LOG.info("拷贝数据包 {}", autoZip.getAbsolutePath());
                    FileUtils.copyFileToDirectory(autoZip, zipDir);
                    path = true;
                }
                List<TTaskData> taskDataList = taskDataService.findTaskData(task.getTaskId());
                if (StringUtils.isBlank(task.getDataPath()) && !taskDataList.isEmpty()) {
                    zipStagedTaskFile(task.getTaskId(), taskDetailVO.getMd5(), zipDir, taskDataList);
                    path = true;
                }
                if (!path) {
                    List<String> result = EngineCommandUtil
                            .runCmdResult("/bin/bash", new String[] {"find " + logCrtlConfigPath + "/ios_check -name " + task.getTaskId() + "_*_data.zip"});
                    if (!CollectionUtils.isEmpty(result)) {
                        File dynamicZip = new File(result.get(0));
                        if (dynamicZip.exists()) {
                            FileUtils.copyFileToDirectory(dynamicZip, zipDir);
                            path = true;
                        }
                    }
                }
            }
            if (path) {
                CommonUtil.compress(zipDir.getAbsolutePath(), zip.getAbsolutePath());
            } else {
                String absolutePath = commonProperties.getProperty("detection.tools.dynamic_path") + taskDetailVO.getMd5();
                CommonUtil.compress(absolutePath, zip.getAbsolutePath());
            }
            LOG.info("下载数据包 {}", zip.getAbsolutePath());
            return zip;
        } catch (IOException e) {
            e.getMessage();
        } finally {
            CommonUtil.deleteFile(zipDir.getAbsolutePath());
        }
        return null;
    }

    /**
     * 打包分阶段数据
     * @param taskId
     * @param md5
     * @param zipDir
     * @throws IOException
     */
    public void zipStagedTaskFile(Long taskId, String md5, File zipDir, List<TTaskData> taskDataList) throws IOException {
        for (TTaskData tTaskData : taskDataList) {
            File stagedZip = new File(
                    commonProperties.getProperty("detection.tools.dynamic_path") +
                            md5 + "_" + taskId + "_AUTO_" + tTaskData.getDynamicSubStatus().getValue() + ".zip");
            if (!stagedZip.exists()) {
                LOG.info("下载阶段数据文件 {}", tTaskData.getDataPath());
                FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(tTaskData.getDataPath(), fastDFSIp)), stagedZip);
            }
            LOG.info("复制阶段数据文件 {}", stagedZip.getAbsolutePath());
            FileUtils.copyFileToDirectory(stagedZip, zipDir);
        }
    }

    @Override
    public File downloadAutoDetectReport(Long taskId, Integer type) throws IjiamiApplicationException {
        ReportResultVO vo = downloadAutoDetectReportPlus(taskId, type);
        return vo == null ? null : vo.report();
    }
    
    @Override
    public File downloadAutoDetectReport(Long taskId, Integer type, TaskReportDownLoadQuery taskReportQuery) throws IjiamiApplicationException {
        ReportResultVO vo = getAutoDetectReportWithCache(taskId, type, taskReportQuery);
        return vo == null ? null : vo.report();
    }
    
    @Override
    public ReportResultVO downloadAutoDetectReportPlus(Long taskId, Integer type) throws IjiamiApplicationException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        TaskDetailVO taskDetailVO = detectionMongodbService.findDetectionResultExcludeField(task.getApkDetectionDetailId(), fields);
        // 组装报告名称
        String outFileName = filenameFilter(CommonUtil.reportDocName(taskDetailVO, "全自动"));
        // 组装返回对象
        ReportResultVO reportResultVO = new ReportResultVO();
        reportResultVO.appName(taskDetailVO.getApk_name());
        reportResultVO.versionCode(taskDetailVO.getApk_version());
        reportResultVO.terminalType(task.getTerminalType());
        // 查询模板
        TReportDesign reportDesign = reportDesignMapper.findByUserIdAndReportObject(task.getCreateUserId(), 3, task.getTerminalType().getValue());
        if (reportDesign == null) {
            reportDesign = reportDesignMapper.findByUserIdAndReportObject(null, 3, task.getTerminalType().getValue());
        }
        Map<String, Object> reportData = new HashMap<>(32);
        reportData.put("reportDesign", reportDesign);
        // android、ios报告 word -> pdf
        //        if (task.getTerminalType() == TerminalTypeEnum.IOS) {
        // 1.word 2.pdf
        if (type == 1 || type == 2) {
            String templateName = reportDesign.getTemplatePath().substring(reportDesign.getTemplatePath().lastIndexOf("/") + 1);
            try{
                cn.ijiami.base.common.file.entity.File logoFile = fileService.findFileByFileKey(reportDesign.getReportLogo());
                reportDesign.setReportLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + logoFile.getFilePath()));
                cn.ijiami.base.common.file.entity.File companyLogoFile = fileService.findFileByFileKey(reportDesign.getCompanyLogo());
                reportDesign.setCompanyLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + companyLogoFile.getFilePath()));
                cn.ijiami.base.common.file.entity.File headerLogoFile = fileService.findFileByFileKey(reportDesign.getHeaderLogo());
                reportDesign.setHeaderLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + headerLogoFile.getFilePath()));
                cn.ijiami.base.common.file.entity.File watermarkLogoFile = fileService.findFileByFileKey(reportDesign.getWatermarkLogo());
                reportDesign.setWatermarkLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + watermarkLogoFile.getFilePath()));
            }catch (Exception e){
                e.getMessage();
            }

            // 存放检测获取到的数据
            reportData.putAll(buildReportData(task.getApkDetectionDetailId(), type));

            Map<String, Object> map = toReportWordData(reportData);
            // 生成word、pdf
            String fileName = generateReport(map, templateName, outFileName, type);
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            File file = new File(reportRootPath + "out" + File.separator + fileName);
            if (file.exists()) {
                // word需要更新页码下标
                if (type == ReportTypeEnum.WORD.getValue()) {
                    String pageUrl = commonProperties.getProperty("ijiami.report.page.url");
                    if (StringUtils.isNoneBlank(pageUrl)) {
                        WordPageUtils.urlPost(pageUrl, file.getAbsolutePath(), file.getAbsolutePath());
                    }
                }
                reportResultVO.report(file);
                return reportResultVO;
            }
        }
        //        }

        // 安卓报告 html -> pdf
        //        if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
        //            reportData.putAll(buildReportData(task));
        //            File html = generateHtml(reportData, outFileName);
        //            if (html == null || !html.exists()) {
        //                throw new IjiamiApplicationException("生成HTML报告失败");
        //            }
        //            // html转pdf报告
        //            if (type == 2) {
        //                File pdf = new File(html.getAbsolutePath() + ".pdf");
        //                String wkPath = "";
        //                if (!System.getProperty("os.name").contains("Windows")) {
        //                    wkPath = commonProperties.getProperty("ijiami.wkhtmltopdf.path");
        //                }
        //                HtmlToPdf.convert(wkPath, "file://" + html.getAbsolutePath() + File.separator + "index.html", pdf.getAbsolutePath());
        //                reportResultVO.report(pdf);
        //                return reportResultVO;
        //            }
        //            // html下载压缩包
        //            File zip = new File(html.getAbsolutePath() + ".zip");
        //            CommonUtil.compress(html.getAbsolutePath(), zip.getAbsolutePath());
        //            reportResultVO.report(zip);
        //            return reportResultVO;
        //        }
        return null;
    }

    /**
     * 优先读取缓存报告
     * @param taskId
     * @param type
     * @param taskReportQuery
     * @return
     * @throws IjiamiApplicationException
     */
    @Override
    public ReportResultVO getAutoDetectReportWithCache(Long taskId, Integer type, TaskReportDownLoadQuery taskReportQuery) throws IjiamiApplicationException {
        // 是否有默认的报告
        Example autoReportQuery = new Example(TTaskAutoReport.class);
        autoReportQuery.createCriteria()
                .andEqualTo("taskId", taskId)
                .andEqualTo("type", type)
                .andEqualTo("isDelete", BooleanEnum.FALSE.value);
        Optional<TTaskAutoReport> autoReport = taskAutoReportMapper.selectByExample(autoReportQuery)
                .stream()
                .filter(report -> isMatchItemNos(report, taskReportQuery)).findFirst();
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (autoReport.isPresent()) {
            LOG.info("TaskId:{} 使用报告缓存", taskId);
            TaskDetailVO taskDetailVO = detectionMongodbService.findByDocumentId(task.getApkDetectionDetailId());
            ReportResultVO reportResultVO = new ReportResultVO();
            reportResultVO.appName(taskDetailVO.getApk_name());
            reportResultVO.versionCode(taskDetailVO.getApk_version());
            reportResultVO.terminalType(task.getTerminalType());
            String fileName = reportFileName(taskDetailVO) + (type == TYPE_WORD ? ".docx" : ".pdf");
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            File file = new File(reportRootPath + "out" + File.separator + fileName);
            try {
                FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(autoReport.get().getDetectReportPath(), fastDFSIntranetIp)), file);
                reportResultVO.report(file);
                return reportResultVO;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            ReportResultVO reportResultVO = downloadAutoDetectReportPlus(taskId, type, taskReportQuery);
            // 判断报告是否需要缓存
            TaskReportDownLoadQuery cacheReport = getTaskReportDownLoadQuery(taskId, task.getTerminalType());
            Collection<String> cacheItemNos = Arrays.asList(cacheReport.getItemNo());
            Collection<String> queryItemNos = Optional.ofNullable(taskReportQuery.getItemNo())
                    .map(arr -> Arrays.stream(arr)
                                      .filter(Objects::nonNull)
                                      .collect(Collectors.toSet()))
                    .orElse(Collections.emptySet());
            
            // 默认勾选的方案一致的才需要缓存
            if (isMatchItemNos(cacheItemNos, queryItemNos)) {
                LOG.info("TaskId:{} 缓存报告", taskId);
                FileVO fileVO = uploadFile(reportResultVO.report().getAbsolutePath());
                if (StringUtils.isNotEmpty(fileVO.getFileUrl())) {
                    insertReportCacheRecord(taskId, fileVO.getFileUrl(), cacheItemNos);
                    LOG.info("TaskId:{} 缓存报告数据保存成功", taskId);
                }
            }
            return reportResultVO;
        }
    }

    @Override
    public void buildAndUploadDefaultReport(Long taskId) {
        // 读取最新的数据
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            LOG.info("任务不存在 不生成报告 taskId:{}", taskId);
            return;
        }
        if (task.getTaskTatus() != DetectionStatusEnum.DETECTION_OVER
                || task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            LOG.info("任务还未完成 暂不生成报告 taskId:{}", task.getTaskId());
            return;
        }
        TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId());
        if (CollectionUtils.isEmpty(taskDetailVO.getDetection_result())) {
            LOG.info("生成并上传自动报告失败 无静态检测数据 taskId:{}", task.getTaskId());
            return;
        }
        TaskReportDownLoadQuery taskReportQuery = getTaskReportDownLoadQuery(task.getTaskId(), task.getTerminalType());
        AutoReportUrls autoDetectReport = buildDefaultReport(task.getTaskId(), taskReportQuery);
        insertReportCacheRecord(task.getTaskId(), autoDetectReport.getDetectReport(), Arrays.asList(taskReportQuery.getItemNo()));
        LOG.info("生成并上传自动报告完成 taskId:{} path={}", task.getTaskId(), autoDetectReport);
    }

    private void insertReportCacheRecord(Long taskId, String reportPath, Collection<String> itemNos) {
        TTaskAutoReport updateReport = new TTaskAutoReport();
        updateReport.setTaskId(taskId);
        updateReport.setDetectReportPath(reportPath);
        updateReport.setReportBuildParams(CommonUtil.beanToJson(itemNos));
        updateReport.setUpdateTime(new Date());
        updateReport.setCreateTime(new Date());
        updateReport.setIsDelete(BooleanEnum.FALSE.value);
        updateReport.setType(TYPE_WORD);
        taskAutoReportMapper.insert(updateReport);
    }

    private boolean isMatchItemNos(TTaskAutoReport report, TaskReportDownLoadQuery taskReportQuery) {
        if (Objects.isNull(taskReportQuery.getItemNo())) {
            // 空的表示获取默认报告数据
            return true;
        }
        Set<String> reportQuery = Arrays.stream(taskReportQuery.getItemNo()).filter(Objects::nonNull).collect(Collectors.toSet());
        List<String> itemNos = CommonUtil.jsonToBean(report.getReportBuildParams(), new TypeReference<List<String>>() {});
        return isMatchItemNos(itemNos, reportQuery);
    }

    private boolean isMatchItemNos(Collection<String> itemNos, Collection<String> reportQuery) {
        if (itemNos.size() != reportQuery.size()) {
            return false;
        }
        List<String> existItemNos = new ArrayList<>(itemNos);
        for (String itemNo:reportQuery) {
            existItemNos.remove(itemNo);
        }
        return existItemNos.isEmpty();
    }

    private String reportFileName(TaskDetailVO taskDetailVO) {
        return filenameFilter(CommonUtil.reportDocName(taskDetailVO, "全自动")).replaceAll(" ","-");
    }
    
    @Override
    public ReportResultVO downloadAutoDetectReportPlus(Long taskId, Integer type, TaskReportDownLoadQuery taskReportQuery) throws IjiamiApplicationException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        TaskDetailVO taskDetailVO = detectionMongodbService.findDetectionResultExcludeField(task.getApkDetectionDetailId(), fields);
        // 组装报告名称
        String outFileName = reportFileName(taskDetailVO);
        // 组装返回对象
        ReportResultVO reportResultVO = new ReportResultVO();
        reportResultVO.appName(taskDetailVO.getApk_name());
        reportResultVO.versionCode(taskDetailVO.getApk_version());
        reportResultVO.terminalType(task.getTerminalType());
        // 查询模板
        TReportDesign reportDesign = reportDesignMapper.findByUserIdAndReportObject(task.getCreateUserId(), 3, task.getTerminalType().getValue());
        if (reportDesign == null) {
            reportDesign = reportDesignMapper.findByUserIdAndReportObject(null, 3, task.getTerminalType().getValue());
        }
        Map<String, Object> reportData = new HashMap<>(32);
        reportData.put("reportDesign", reportDesign);


        String selectItemNo[] = taskReportQuery.getItemNo();

        // 1.word 2.pdf
        if (type == 1 || type == 2) {
            String templateName = reportDesign.getTemplatePath().substring(reportDesign.getTemplatePath().lastIndexOf("/") + 1);
            try {
				reportDesign.setReportLogo(getImageBASE64ByBaseFile(reportDesign.getReportLogo()));
				reportDesign.setCompanyLogo(getImageBASE64ByBaseFile(reportDesign.getCompanyLogo()));
				reportDesign.setHeaderLogo(getImageBASE64ByBaseFile(reportDesign.getHeaderLogo()));
				reportDesign.setWatermarkLogo(getImageBASE64ByBaseFile(reportDesign.getWatermarkLogo()));
			} catch (Exception e) {
				e.getMessage();
			}
            // 存放检测获取到的数据
            reportData.putAll(buildReportData(task.getApkDetectionDetailId(), type, selectItemNo,taskReportQuery.getLawId()));

            Map<String, Object> map = toReportWordData(reportData);
            // 生成word、pdf
            String fileName = generateReport(map, templateName, outFileName, type);
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            File file = new File(reportRootPath + "out" + File.separator + fileName);
            if (file.exists()) {
                // word需要更新页码下标
                if (type == ReportTypeEnum.WORD.getValue()) {
                    String pageUrl = commonProperties.getProperty("ijiami.report.page.url");
                    if (StringUtils.isNoneBlank(pageUrl)) {
                        WordPageUtils.urlPost(pageUrl, file.getAbsolutePath(), file.getAbsolutePath());
                    }
                }
                reportResultVO.report(file);
                return reportResultVO;
            }
        }
        return null;
    }

    private String getImageBASE64ByBaseFile(String fileKey) {
        cn.ijiami.base.common.file.entity.File image = fileService.findFileByFileKey(fileKey);
        File imageFile = new File(commonProperties.getFilePath() + image.getFilePath());
        if (!imageFile.exists()) {
            return null;
        }
        return reportChartService.getImageBASE64(imageFile.getAbsolutePath());
    }

   //windows文件名非法关键词过滤
    private static Pattern FilePattern = Pattern.compile("[\\\\/:*?\"<>|]");
    public static String filenameFilter(String str) {
    	return str==null?null:FilePattern.matcher(str).replaceAll("-");
    }

    private Map<String, Object> toReportWordData(Object reportData) {
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        // 只移除了影响FreeMarker生成word的乱码，有一些其他乱码没移除
        String json = gson.toJson(reportData);
        String characters = WordStringUtil.converCharacters(json);
        return gson.fromJson(characters, new TypeToken<HashMap<String, Object>>() {
        }.getType());
    }

    /**
     * 生成HTML报告
     *
     * @param data        数据
     * @param outFileName 输出的文件名
     * @return 生成后的报告文件夹
     * @throws IjiamiApplicationException
     */
    private File generateHtml(Map<String, Object> data, String outFileName) throws IjiamiApplicationException {
        File templateDir = new File(commonProperties.getProperty("ijiami.report.root.path") + "reportTemplate" + File.separator + "html");
        File outRoot = new File(commonProperties.getProperty("ijiami.report.root.path") + "out");

        try {
            CopyDirUtil.copyFolder(templateDir.getAbsolutePath(), outRoot.getAbsolutePath());
            File outFile = new File(outRoot + File.separator + outFileName);
            boolean bool = new File(outRoot.getAbsolutePath() + File.separator + "html").renameTo(outFile);
            if (bool) {
                Configuration configuration = new Configuration();
                configuration.setDefaultEncoding("UTF-8");
                configuration.setDirectoryForTemplateLoading(templateDir);
                Template template = configuration.getTemplate("/index.html");
                FileWriter fileWriter = new FileWriter(outFile.getAbsolutePath() + File.separator + "index.html");
                template.process(data, fileWriter);
                fileWriter.flush();
                fileWriter.close();
                return outFile;
            }
            return null;
        } catch (IOException | TemplateException e) {
            e.getMessage();
            throw new IjiamiApplicationException("生成html报告失败");
        }
    }

    /**
     * html报告需要的数据
     *
     * @param task 任务
     * @return 数据
     * @throws IjiamiApplicationException 全局异常
     */
    private Map<String, Object> buildReportData(TTask task) throws IjiamiApplicationException {
        Map<String, Object> dataMap = new HashMap<>();
        BaseMessageVO appBaseInfo = this.getAppBaseInfo(task.getApkDetectionDetailId());
        List<SdkVO> sdkList = this.getSDKList(task.getApkDetectionDetailId(), task.getTaskId());
        List<TPrivacySensitiveWord> privacySensitiveWords = privacySensitiveWordService.findByTaskId(task.getTaskId(), 0);
        List<TPrivacySharedPrefs> sharedPrefs = privacySharedPrefsMapper.findByTaskId(task.getTaskId(), 0);
        List<PrivacyActionNougatVO> actionNougats =
                privacyActionNougatService.countActionNougatByTaskId(task.getTaskId(), BehaviorStageEnum.BEHAVIOR_FRONT.getValue());
        List<TPrivacyOutsideAddress> outSideAddress = privacyOutsideAddressService.getOutSideAddress(task.getTaskId(), 0);

        for (SdkVO sdkVO : sdkList) {
            if (StringUtils.isBlank(sdkVO.getIpAddress())) {
                continue;
            }
            for (TPrivacyOutsideAddress address : outSideAddress) {
                if (StringUtils.isBlank(address.getIp()) || StringUtils.isBlank(address.getHost())) {
                    continue;
                }
                if (sdkVO.getIpAddress().contains(address.getIp()) || sdkVO.getIpAddress().contains(address.getHost())) {
                    sdkVO.getOutsideAddresses().add(address);
                }
            }
        }

        HtmlReportCommonVO htmlReportCommonVO = countPermissionActionIp(task, actionNougats, outSideAddress);
        TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
        cn.ijiami.base.common.file.entity.File logoFile = fileService.findFileByFileKey(assets.getLogo());
        dataMap.put("logo", reportChartService.getImageBASE64(commonProperties.getFilePath() + logoFile.getFilePath()));
        dataMap.put("appBaseInfo", appBaseInfo);
        dataMap.put("detectionTime", DateUtils.getDistanceTime(task.getTaskEndtime(), task.getTaskStarttime()));
        dataMap.put("htmlReportCommonVO", htmlReportCommonVO);
        dataMap.put("permissions", countPermissionUsed(task));
        dataMap.put("outSideAddress", outSideAddress);
        dataMap.put("privacySensitiveWords", privacySensitiveWords);
        dataMap.put("sharedPrefs", sharedPrefs);
        dataMap.put("actionNougats", actionNougats);
        dataMap.put("sdkList", sdkList);
        return dataMap;
    }

    @Override
    public Map<String, Object> buildReportData(String documentId, Integer type) {
    	return buildReportData(documentId, type, null);
    }
    
    @Override
    public Map<String, Object> buildReportData(String documentId, Integer type,String itemNo[]) {
    	return buildReportData(documentId, type, itemNo,null);
    }
    
    
    /**
     * 存在风险	1001
     * 未发现风险	1002
     * 权限使用情况	2001
     * 应用行为分析	2002
     * 通讯行为分析	2003
     * 传输个人信息	2004
     * 存储个人信息	2005
     * SDK分析	2006
     * 个人信息风险漏洞	2007
     */
    @Override
    public Map<String, Object> buildReportData(String documentId, Integer type, String[] itemNo, Integer lawId) {
        Map<String, Object> dataMap = new HashMap<>();
        long time1 = System.currentTimeMillis();
        Set<String> itemNoList = Objects.isNull(itemNo) ? Collections.emptySet() : Sets.set(itemNo);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            TTask taskEntity = new TTask();
            taskEntity.setDetectComplete(null);
            taskEntity.setApkDetectionDetailId(documentId);
            TTask task = taskMapper.selectOne(taskEntity);

            int behavior = BehaviorStageEnum.BEHAVIOR_FRONT.getValue();
            if (task.getTerminalType() == TerminalTypeEnum.IOS && task.getDetectionType()==TaskDetectionTypeEnum.DEPTH.getValue()) {
                behavior = 0;
            }


            BaseMessageVO appBaseInfo = this.getAppBaseInfo(documentId);
            long time2 = System.currentTimeMillis();
            LOG.info("生成报告.1数据组装.耗时:{}", (time2-time1));
            List<SdkVO> sdkList = this.getSDKList(documentId, task.getTaskId());
            long time3 = System.currentTimeMillis();
            LOG.info("生成报告.2获取SDK.耗时:{}", (time3-time2));
            PrivacyCategoryVO categoryVO = apkCategoryService.findByTaskId(task.getTaskId());
            String categories = "";
            if (categoryVO != null && categoryVO.getPrivacyCategoryNames() != null) {
                categories = String.join(",", categoryVO.getPrivacyCategoryNames());
            }

            List<PrivacyCheckVO> privacyCheckVOList = privacyCheckService.selectPrivacyPolicy(task.getTaskId(), type, task.getTerminalType().getValue());
            List<PrivacyPolicyTypeVO> policyTypeVOList = privacyCheckService.findByTaskIdAndType(task.getTaskId(), type, task.getTerminalType().getValue());

            List<TPrivacySensitiveWord> cookies = privacySensitiveWordService.findByCookie(task.getTaskId(), behavior);
            // 传输个人信息
            List<TPrivacySensitiveWord> privacySensitiveWords = privacySensitiveWordService.findByTaskId(task.getTaskId(), behavior);
            List<PrivacyPolicyImgVO> policyImgs = privacyPolicyImgService.findPolicyImgsByTaskId(task.getTaskId());
            List<SensitiveWordImgVO> sensitiveWordImgs = privacySensitiveWordService.findImgByTaskId(task.getTaskId(), behavior);
            TPrivacyPolicyType law = privacyCheckService.findLawByType(task.getTaskId(), type,task.getTerminalType().getValue());
            long time4 = System.currentTimeMillis();
            LOG.info("生成报告.3查询传输个人信息.耗时:{}", (time4-time3));
            // 存储个人信息
            List<TPrivacySharedPrefs> sharedPrefs = privacySharedPrefsMapper.findByTaskId(task.getTaskId(), behavior);
            Iterator<TPrivacySharedPrefs> iterator = sharedPrefs.iterator();
            while (iterator.hasNext()) {
                TPrivacySharedPrefs sharedPref = iterator.next();
            	if(sharedPref.getResultStatus()==2){
                    iterator.remove();
            	}
                String content = sharedPref.getContent();
                if (content != null && (content.lastIndexOf("<") > content.indexOf(">"))) {
                    sharedPref.setContent(content.substring(content.indexOf(">") + 1, content.lastIndexOf("<")));
                }
            }
            privacySensitiveWords.forEach(this::removeNoACIIChar);
            cookies.forEach(this::removeNoACIIChar);
            
            // app基本信息
            dataMap.put("appBaseInfo", appBaseInfo);
            // 应用类型类别
            dataMap.put("categories", categories);
            // 检测类型
            dataMap.put("detectionType", task.getDetectionType());
            // 法律法规类型
            dataMap.put("law", law);
            
            long time5 = System.currentTimeMillis();
            LOG.info("生成报告.4查询存储个人信息.耗时:{}", (time5-time4));
            //权限使用情况	2001
            if(itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_2001)) {
            	// 声明权限
                dataMap.put("xmlPermission", countXmlPermission(task));
                // 未声明权限
                dataMap.put("noDeclaredPermission", countNoDeclaredPermission(task));
                // 敏感权限
                dataMap.put("sensitivePermission", countSensitivePermission(task));
                // 过度权限
                dataMap.put("excessPermission", countExcessPermission(task, behavior));
            }
            long time6 = System.currentTimeMillis();
            LOG.info("生成报告.5查询权限使用情况.耗时:{}", (time6-time5));
            if(itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_2003)) {
            	// 通讯行为
                dataMap.put("outSideAddress", countOutsideAddress(task.getTaskId(), behavior));
            }
            
            
            CountActionNougatVO actionNougatVO = countActionNougat(task.getTaskId(), behavior); //默认前台行为数据
            long time7 = System.currentTimeMillis();
            LOG.info("生成报告.6查询默认前台行为数据.耗时:{}", (time7-time6));
            
            //组装SDK行为分析
            sdkActionNougat(sdkList, actionNougatVO, task.getTaskId());
            //组装SDK存储个人信息
            sdkSharedPrefs(sdkList, sharedPrefs);
            long time8 = System.currentTimeMillis();
            LOG.info("生成报告.7查询组装SDK行为分析.耗时:{}", (time8-time7));
            if(itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_2002)) {
            	// 应用行为分析
                dataMap.put("privacyActionNougats", actionNougatVO); //前台行为数据
                dataMap.put("actionNougatGrantVO", countActionNougat(task.getTaskId(), BehaviorStageEnum.BEHAVIOR_GRANT.getValue()));//授权前数据
                dataMap.put("actionNougatGroundVO", countActionNougat(task.getTaskId(), BehaviorStageEnum.BEHAVIOR_GROUND.getValue()));//后台数据
                dataMap.put("actionNougatExitVO", countActionNougat(task.getTaskId(), BehaviorStageEnum.BEHAVIOR_EXIT.getValue()));//退出数据
            }
            long time9 = System.currentTimeMillis();
            LOG.info("生成报告.8查询应用行为分析.耗时:{}", (time9-time8));
            // 敏感词
            dataMap.put("countSensitiveWord", countSensitiveWord(task.getTaskId(), behavior));
            if(itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_2004)) {
            	// 传输个人信息
                dataMap.put("privacySensitiveWords", privacySensitiveWords);
                
                //传输个人信息
                List<ReprotActionSharedPrefsVO> sensitiveWordTypeList = sensitiveWordTypeList(task.getTaskId(), behavior);
                dataMap.put("sensitiveWordTypeList", sensitiveWordTypeList);
            }
            long time10 = System.currentTimeMillis();
            LOG.info("生成报告.9查询敏感词.耗时:{}", (time10-time9));
            if(itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_2005)) {
            	 //存储个人信息
                List<ReprotActionSharedPrefsVO> sharedPrefsTypeList = sharedPrefsTypeList(task.getTaskId(), behavior);
                dataMap.put("sharedPrefsTypeList", sharedPrefsTypeList);
                // 存储个人信息
                dataMap.put("sharedPrefs", sharedPrefs);
            }
            long time11 = System.currentTimeMillis();
            LOG.info("生成报告.10存储个人信息.耗时:{}", (time11-time10));
            // 未使用
            dataMap.put("cookie", cookies);
            // 未使用
            dataMap.put("sensitiveWordImgs", sensitiveWordImgs);
            // SDK分析
            if (itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_2006)) {
                sdkListPermissions(sdkList);//SDK权限数据重新组装
                setSDKInfo(dataMap, sdkList);
                dataMap.put("sdkList", sdkList);
            }
            long time12 = System.currentTimeMillis();
            LOG.info("生成报告.11SDK分析.耗时:{}", (time12-time11));
            // 模拟传感器摇一摇
            List<ReportShakeItemDTO> analogShakeValues = getReportAnalogSensorShakeItemList(task.getTaskId());
            if (!CollectionUtils.isEmpty(analogShakeValues)) {
                dataMap.put("analogShakeList", analogShakeValues);
            }
           
            List<THardwareSensorShakeValue> hardwareShakeValues = getReportHardwareSensorShakeItemList(task.getTaskId());
            if (!CollectionUtils.isEmpty(hardwareShakeValues)) {
                dataMap.put("hardwareShakeList", hardwareShakeValues);
            }
            long time13 = System.currentTimeMillis();
            LOG.info("生成报告.12查询模拟传感器摇一摇.耗时:{}", (time13-time12));
            // 法规检测项，检测列表
            dataMap.put("privacyCheckVOList", privacyCheckVOList);
            // 法规检测项，检测详情
            dataMap.put("policyTypeVOList", policyTypeVOList);
            // 检测图
            dataMap.put("policyImgs", policyImgs);
            
            if (itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_1001) || itemNoList.contains(ConstantsUtils.REPORT_NUM_1002)) {
                //1存在风险 2安全
                Integer resultStatus164 = itemNoResultStatus164(itemNo);
                Integer lawId164 = getLaw164ByTerminalType(task.getTerminalType());
                // 164号文 OR 191
                dataMap.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(Long.valueOf(lawId164),
                        task.getTaskId(), resultStatus164, true));
                dataMap.put("miitLawDetectResultMap", groupAllItemActionByTaskId(task.getTaskId(), lawId164));
                dataMap.put("lawId", lawId164);
            }
            
            if(itemNoList.isEmpty() || itemNoList.contains(ConstantsUtils.REPORT_NUM_3001) || itemNoList.contains(ConstantsUtils.REPORT_NUM_3002)) {
                Integer resultStatus191 = itemNoResultStatus191(itemNo);
                Integer lawId191 = getLaw191ByTerminalType(task.getTerminalType());
                // 191号文
                dataMap.put("miitLawInfo2", miitDetectService.findLawDetectResultByTaskId(Long.valueOf(lawId191),
                        task.getTaskId(), resultStatus191, true));
                dataMap.put("miitLawDetectResultMap2", groupAllItemActionByTaskId(task.getTaskId(), lawId191));
                dataMap.put("lawId_191", lawId191);
            }

            if(itemNoList.isEmpty() || itemNoList.contains(REPORT_NUM_5001) || itemNoList.contains(ConstantsUtils.REPORT_NUM_5002)) {
                Integer resultStatus35273 = itemNoResultStatus35273(itemNo);
                Integer lawId35273 = getLaw35273ByTerminalType(task.getTerminalType());
                // 35273
                dataMap.put("miitLawInfo3", miitDetectService.findLawDetectResultByTaskId(lawId35273.longValue(),
                        task.getTaskId(), resultStatus35273, true));
                dataMap.put("miitLawDetectResultMap3", groupAllItemActionByTaskId(task.getTaskId(), lawId35273));
                dataMap.put("lawId_35273", lawId35273);
            }
            if(itemNoList.isEmpty() || itemNoList.contains(REPORT_NUM_6001)
                    || itemNoList.contains(REPORT_NUM_6002)) {
                Integer resultStatus41391 = itemNoResultStatus41391(itemNo);
                Integer lawId41391 = getLaw41391ByTerminalType(task.getTerminalType());
                // 41391
                dataMap.put("miitLawInfo4", miitDetectService.findLawDetectResultByTaskId(lawId41391.longValue(),
                        task.getTaskId(), resultStatus41391, true));
                dataMap.put("miitLawDetectResultMap4", groupAllItemActionByTaskId(task.getTaskId(), lawId41391));
                dataMap.put("lawId_41391", lawId41391);
            }
            
            long time14 = System.currentTimeMillis();
            LOG.info("生成报告.13查询法规.耗时:{}", (time14-time13));
            // 检测起始时间
            dataMap.put("startTime", format.format(task.getTaskStarttime()));
            // 检测耗时
            dataMap.put("detectionTime", DateUtils.getDistanceTime(task.getTaskEndtime(), task.getTaskStarttime()));
            
            dataMap.put("selectItemNo", StringUtils.join(itemNo, ","));
            
            dataMap.put("taskId", task.getTaskId());

            Map<String,String> detectionPhoneInfo = new HashMap<>();
            detectionPhoneInfo.put("phoneBrand", "-");         //手机品牌
            detectionPhoneInfo.put("phoneModel", "-");         //手机型号
            detectionPhoneInfo.put("phoneSystemVersion", "Android8.1"); //手机系统版本
            
            //检测手机环境
            dataMap.put("detectionPhoneInfo", detectionPhoneInfo);

            //android 检测项目结果分析   启用VPN服务检测"0803",SSL证书有效性"0705",访问境外服务风险"0806",webview明文存储密码风险"0503", http传输风险"0704", 秘钥编码风险"0609", 资源文件泄露风险"0605", 键盘使用风险"0702"
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID || task.getTerminalType() == TerminalTypeEnum.HARMONY) {
                // 12.检测结果分析
                //status 1安全 2存在风险  
                Integer status = null;
                if (itemNo == null || itemNo.length == 0 || StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_2007)) {
                    List<DetectionChildResultDetailVO> detectionResultDetailVOList;
                    if (task.getTerminalType() == TerminalTypeEnum.HARMONY) {
                        detectionResultDetailVOList = getHarmonyDetectionDetail(documentId, status);
                    } else {
                        detectionResultDetailVOList = getDetectionDetail(documentId, status);
                    }
                    dataMap.put("detectionResultDetailVOList", detectionResultDetailVOList);
                }
            }
            //IOS-检测项目结果分析   SSL证书有效性"0601", http传输风险"0602",  键盘使用风险"0407"
            if (task.getTerminalType() == TerminalTypeEnum.IOS) {
                if (itemNo == null || itemNo.length == 0 || StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_2007)) {
					List<TaskResultVO> resultList = detectionMongodbService.getIOSResults(documentId);
					dataMap.put("detectionResultDetailVOList", resultList);
                }
            }
            if (task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) {
                if (itemNo == null || itemNo.length == 0 || StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_2008)) {
                    List<TComplianceAppletPlugins> resultList = complianceAppletPluginsMapper.selectAllPluginsByTaskId(task.getTaskId());
                    dataMap.put("appletPlugins", resultList);
                }
            }
            if (task.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
                if (itemNo == null || itemNo.length == 0 || StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_2009)) {
                    dataMap.put("appletServices", appletService.findAlipayAppletService(task.getTaskId()));
                }
            }
            
            long time15 = System.currentTimeMillis();
            LOG.info("生成报告.13风险漏洞数据.耗时:{}", (time15-time14));
        } catch (IjiamiApplicationException e) {
            e.getMessage();
        }
        long time2 = System.currentTimeMillis();
        LOG.info("生成报告组装数据耗时={}ms", time2 - time1);
        return dataMap;
    }

    private Map<String, LawDetectDetailDTO> groupAllItemActionByTaskId(Long taskId, Integer lawId) {
        return miitDetectService.findAllItemByTaskId(taskId, lawId);
    }

    /**
     * 根据行为阶段、主体名称、行为名称做筛选主要条件，取最高的触发频率，作为唯一数据展示。
     * @param actionDetailVOS
     * @return
     */
    private List<LawActionDetailDTO> groupItemAction(List<LawActionDetailDTO> actionDetailVOS) {
        return actionDetailVOS
                .stream()
                .collect(Collectors.groupingBy(this::groupItemActionKey))
                .values()
                .stream()
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream().max(Comparator.comparingInt(this::getTriggerNum)).get())
                .collect(Collectors.toList());
    }

    private Integer getTriggerNum(LawActionDetailDTO detailVO) {
        return Objects.isNull(detailVO.getTriggerNum()) ? 0 : detailVO.getTriggerNum();
    }

    private String groupItemActionKey(LawActionDetailDTO detailVO) {
        return "" + detailVO.getBehaviorStage() + detailVO.getExecutorType() +
                detailVO.getExecutor() + detailVO.getActionName();
    }

    private List<ReportShakeItemDTO> getReportAnalogSensorShakeItemList(Long taskId) {
        List<TDeepShakeValue> deepShakeValues = deepShakeValueMapper.getShakeValueByTaskId(taskId);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return deepShakeValues.stream().map(v -> {
            List<ShakeParam> paramList = CommonUtil.jsonToBean(v.getAccelerationValue(), new TypeReference<List<ShakeParam>>() {
            });

            StringJoiner paramsBuilder = new StringJoiner("\n");
            paramsBuilder.add("加速度传感器：");
            for (int i = 0; i < paramList.size(); i++) {
                ShakeParam param = paramList.get(i);
                paramsBuilder.add(String.format("第%d组:X轴加速度: %s m/s^2；Y轴加速度: %s m/s^2；Z轴加速度: %s m/s^2；", i + 1, param.getX(), param.getY(), param.getZ()));
            }
            List<ShakeParam> gyroscopeList;
            if(!ObjectUtils.isEmpty(v.getGyroscopeValue())){
                gyroscopeList = CommonUtil.jsonToBean(v.getGyroscopeValue(), new TypeReference<List<ShakeParam>>() {});
                paramsBuilder.add("陀螺仪传感器：");
                if (Objects.nonNull(gyroscopeList)) {
                    for(int j = 0; j < gyroscopeList.size(); j ++){
                        ShakeParam param = gyroscopeList.get(j);
                        paramsBuilder.add(String.format("第%d组:X轴角速度: %s rad/s；Y轴角速度: %s rad/s；Z轴角速度: %s rad/s；", j + 1, param.getX(), param.getY(), param.getZ()));
                    }
                }
            }
            return new ReportShakeItemDTO(dateFormat.format(v.getCreateTime()), paramsBuilder.toString(), v.getFlag());
        }).collect(Collectors.toList());
    }

    private List<THardwareSensorShakeValue> getReportHardwareSensorShakeItemList(Long taskId) {
        return hardwareSensorShakeValueMapper.getShakeValueByTaskId(taskId);
    }
    
    private void setSDKInfo(Map<String, Object> dataMap,List<SdkVO> sdkList){
    	
    	Map<String,Object> map = new HashMap<>();
    	int isPersonalActionNougatCount = 0;
    	int isPersonalPermissionCount = 0;
    	int outsideIpCount = 0;
    	if(sdkList != null && sdkList.size()>0) {
    		for (SdkVO sdkVO : sdkList) {
        		isPersonalActionNougatCount = isPersonalActionNougatCount + sdkVO.getIsPersonalActionNougatCount();
        		isPersonalPermissionCount = isPersonalPermissionCount + sdkVO.getIsPersonalPermissionCount();
        		outsideIpCount = outsideIpCount + sdkVO.getOutsideIpCount();
    		}
    	}
    	
    	map.put("sdkTotalCount", sdkList==null?0:sdkList.size());
    	map.put("isPersonalActionNougatCount", isPersonalActionNougatCount);
    	map.put("isPersonalPermissionCount", isPersonalPermissionCount);
    	map.put("outsideIpCount", outsideIpCount);
    	
    	dataMap.put("sdkBaseInfo", map);
    }
    
   /**
    * 传输个人信息列表
    */
    private List<ReprotActionSharedPrefsVO> sensitiveWordTypeList(Long taskId, int behavior){
    	List<ReprotActionSharedPrefsVO> list = new ArrayList<>();
    	//传输个人信息
        List<CountSensitiveTypeVO> sensitiveWordTypeList = privacySensitiveWordService.countSensitiveTypeByTaskId(taskId, behavior);
        if(sensitiveWordTypeList == null || sensitiveWordTypeList.size()==0) {
        	return list;
        }
        for (CountSensitiveTypeVO countSensitiveTypeVO : sensitiveWordTypeList) {
        	ReprotActionSharedPrefsVO vo = new ReprotActionSharedPrefsVO();
        	vo.setTypeName(countSensitiveTypeVO.getTypeName());
//    		List<CountSensitiveNameVO> voList = privacySensitiveWordService.countSensitiveNameByTaskId(taskId, countSensitiveTypeVO.getTypeId(), behavior);
//    		for (CountSensitiveNameVO countSensitiveNameVO : voList) {
				vo.setAppCount(countSensitiveTypeVO.getAppCount());
				vo.setSdkCount(countSensitiveTypeVO.getSdkCount());
				vo.setName(countSensitiveTypeVO.getName());
//			}
    		list.add(vo);
        }
        return list;
    }
    
    /**
     * 存储个人信息列表
     */
    private List<ReprotActionSharedPrefsVO> sharedPrefsTypeList(Long taskId, int behavior){
    	List<ReprotActionSharedPrefsVO> list = new ArrayList<>();
    	//存储个人信息
        List<CountSharedPrefsTypeVO> sharedPrefsTypeList =  privacySharedPrefsService.countSharedPrefsTypeByTaskId(taskId, behavior);
        if(sharedPrefsTypeList == null || sharedPrefsTypeList.size()==0) {
        	return list;
        }
        for (CountSharedPrefsTypeVO countSharedPrefsTypeVO : sharedPrefsTypeList) {
        	
        	ReprotActionSharedPrefsVO vo = new ReprotActionSharedPrefsVO();
        	vo.setTypeName(countSharedPrefsTypeVO.getTypeName());
			vo.setAppCount(countSharedPrefsTypeVO.getAppCount());
			vo.setSdkCount(countSharedPrefsTypeVO.getSdkCount());
			vo.setName(countSharedPrefsTypeVO.getName());
    		list.add(vo);
        }
        return list;
    }
    
    /**
     * 判断查询数据包含合规状态 （合规或者不合规）
     * @param itemNo  
     * @return  1存在风险 2未发现风险
     */
    private Integer itemNoResultStatus164(String itemNo[]){
    	Integer resultStatus = null;
    	if(itemNo==null || itemNo.length==0) {
    		return resultStatus;
    	}
        //存在风险
        if(StringUtils.join(itemNo, ",").contains("1001")){
        	resultStatus = 1;
        }
        //未发现风险
        if(StringUtils.join(itemNo, ",").contains("1002")){
        	resultStatus = 2;
        }
        //存在风险和合规的数据
        if((StringUtils.join(itemNo, ",").contains("1001") && StringUtils.join(itemNo, ",").contains("1002"))){
        	resultStatus = null;
        }
    	return resultStatus;
    }
    
    private Integer itemNoResultStatus191(String itemNo[]){
    	Integer resultStatus = null;
    	if(itemNo==null || itemNo.length==0) {
    		return resultStatus;
    	}
        //存在风险
        if(StringUtils.join(itemNo, ",").contains("3001")){
        	resultStatus = 1;
        }
        //未发现风险
        if(StringUtils.join(itemNo, ",").contains("3002")){
        	resultStatus = 2;
        }
        //存在风险和合规的数据
        if((StringUtils.join(itemNo, ",").contains("3001") && StringUtils.join(itemNo, ",").contains("3002"))){
        	resultStatus = null;
        }
    	return resultStatus;
    }

    private Integer itemNoResultStatus35273(String[] itemNo){
        if (itemNo==null || itemNo.length==0) {
            return null;
        }
        List<String> itemNoList = Arrays.asList(itemNo);
        //存在风险和合规的数据
        if (itemNoList.contains(REPORT_NUM_5001) && itemNoList.contains(REPORT_NUM_5002)){
            return null;
        }
        //存在风险
        if (itemNoList.contains(REPORT_NUM_5001)){
            return LawResultStatusEnum.NON_COMPLIANCE.getValue();
        }
        //未发现风险
        if (itemNoList.contains(REPORT_NUM_5002)){
            return LawResultStatusEnum.NON_INVOLVED.getValue();
        }
        return null;
    }

    private Integer itemNoResultStatus41391(String[] itemNo){
        if (itemNo==null || itemNo.length==0) {
            return null;
        }
        List<String> itemNoList = Arrays.asList(itemNo);
        //存在风险和合规的数据
        if (itemNoList.contains(REPORT_NUM_6001) && itemNoList.contains(REPORT_NUM_6002)){
            return null;
        }
        //存在风险
        if (itemNoList.contains(REPORT_NUM_6001)){
            return LawResultStatusEnum.NON_COMPLIANCE.getValue();
        }
        //未发现风险
        if (itemNoList.contains(REPORT_NUM_6002)){
            return LawResultStatusEnum.NON_INVOLVED.getValue();
        }
        return null;
    }

    private static final Pattern NON_ASCII_CHAR_PATTERN = Pattern.compile("[^\\x0A\\x0D\\x20-\\x7E]");

    private void removeNoACIIChar(TPrivacySensitiveWord word) {
        if (StringUtils.isNotBlank(word.getDetailsData())) {
            word.setDetailsData(NON_ASCII_CHAR_PATTERN.matcher(word.getDetailsData()).replaceAll(""));
        }
        if (StringUtils.isNotBlank(word.getCode())) {
            word.setCode(NON_ASCII_CHAR_PATTERN.matcher(word.getCode()).replaceAll(""));
        }
    }

    private void removeNoACIIChar(TPrivacyOutsideAddress address) {
        if (StringUtils.isNotBlank(address.getDetailsData())) {
            address.setDetailsData(NON_ASCII_CHAR_PATTERN.matcher(address.getDetailsData()).replaceAll(""));
        }
    }


    private void sdkListPermissions(List<SdkVO> sdkList) {
        if (sdkList == null || sdkList.size() == 0) {
            return;
        }
        for (SdkVO sdkVO : sdkList) {
            List<TPrivacyActionNougat> listActionNougatList = sdkVO.getActionNougatList();
            List<PermissionVO> listPermissions = sdkVO.getPermissions();
            List<String> pList = permissionNameList(listPermissions);
            List<TPrivacyOutsideAddress> outsideList = sdkVO.getOutsideAddresses();
            
            Integer isPersonalActionNougatCount = 0; //与个人信息相关行为（单位：种）
            Integer isPersonalPermissionCount = 0;   //与个人信息相关权限（单位：个）	
            Integer outsideIpCount = 0;              //通信境外IP（单位：个）
            Set<String> actionNougatType = new HashSet<>();
            
            if (listActionNougatList != null && listActionNougatList.size() == 0) {
	            for (TPrivacyActionNougat nougat : listActionNougatList) {
	                
	            	if(StringUtils.isNoneBlank(nougat.getIsPersonal()) && "1".equals(nougat.getIsPersonal())) {
	            		actionNougatType.add(nougat.getActionName());
	            	}
	            	
	            	if (StringUtils.isBlank(nougat.getActionPermission())) {
	                    continue;
	                }
	                if (pList.contains(nougat.getActionPermission() + nougat.getActionName())) {
	                    continue;
	                }
	                PermissionVO p = new PermissionVO();
	                p.setName(nougat.getActionPermission());
	                p.setRemark(nougat.getActionName());
	                p.setIsPrivacy(nougat.getIsPersonal()==null? 0 :Integer.valueOf(nougat.getIsPersonal()));
	                p.setUseCount(nougat.getCounter());
	                p.setType(nougat.getSensitive() ? 1 : 0);
	                listPermissions.add(p);
	            }
	            isPersonalActionNougatCount = actionNougatType.size();
            }
            
            if(listPermissions != null && listPermissions.size()>0) {
            	for (PermissionVO permission : listPermissions) {
    				if(permission.getIsPrivacy() != null && permission.getIsPrivacy() == PrivacyStatusEnum.YES.getValue()) {
    					isPersonalPermissionCount++;
    				}
    			}
            }
            
            if(outsideList != null && outsideList.size()>0) {
            	for (TPrivacyOutsideAddress tPrivacyOutsideAddress : outsideList) {
                    if (tPrivacyOutsideAddress.getOutside() != null && tPrivacyOutsideAddress.getOutside() == PrivacyStatusEnum.YES.getValue()) {
                        outsideIpCount++;
                    }
                }
            }
            
            sdkVO.setIsPersonalActionNougatCount(isPersonalActionNougatCount);
            sdkVO.setOutsideIpCount(outsideIpCount);
            sdkVO.setIsPersonalPermissionCount(isPersonalPermissionCount);
        }
    }

    private List<String> permissionNameList(List<PermissionVO> listPermissions) {
        List<String> list = new ArrayList<>();
        for (PermissionVO permissionVO : listPermissions) {
            list.add(permissionVO.getName() + permissionVO.getRemark());
        }
        return list;
    }

    private void sdkActionNougat(List<SdkVO> sdkList, CountActionNougatVO actionNougatVO, Long taskId) {
        //        List<TPrivacyActionNougat> actionList = actionNougatVO.getPrivacyActionNougats();
        List<TPrivacyActionNougat> privacyActionNougats = privacyActionNougatService.countActionByTaskId(taskId, BehaviorStageEnum.BEHAVIOR_FRONT.getValue());
        if (privacyActionNougats == null || privacyActionNougats.size() == 0) {
            return;
        }
        if(sdkList==null) {
        	return;
        }
        for (SdkVO sdkVO : sdkList) {
            if (StringUtils.isEmpty(sdkVO.getPackageName())) {
                continue;
            }
            List<TPrivacyActionNougat> actionNougatList = new ArrayList<>();
            for (TPrivacyActionNougat actionNougat : privacyActionNougats) {
                if (actionNougat.getExecutorType() != 2 || StringUtils.isEmpty(actionNougat.getExecutor())) {
                    continue;
                }

                // 保存sdkId之后
                String sdkIdsJson = Optional.ofNullable(actionNougat.getSdkIds()).orElse("[0]");
                List<Long> sdkIds = JSON.parseArray(sdkIdsJson, Long.class);
                if (sdkIds.contains(sdkVO.getId())) {
                    actionNougatList.add(actionNougat);
                    continue;
                }

                // 修复，因为包名匹配引起的堆栈数据缺失问题，未保存SDK之前
                String[] executors = actionNougat.getExecutor().split(",");
                List<String> executorList = new ArrayList<>(executors.length);
                Collections.addAll(executorList, executors);
                if (executorList.contains(sdkVO.getName())) {
                    actionNougatList.add(actionNougat);
                }
            }
            if (CollectionUtils.isEmpty(actionNougatList)) {
                continue;
            }
            // 合并统计数据
            List<TPrivacyActionNougat> transitions = new ArrayList<>();
            actionNougatList.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getActionId)).forEach((actionId, actionNougats) -> {
                actionNougats.stream().reduce((a, b) -> {
                    TPrivacyActionNougat actionNougat = new TPrivacyActionNougat();
                    BeanUtils.copyProperties(a, actionNougat);
                    int aNum = Optional.ofNullable(a.getCounter()).orElse(0);
                    int bNum = Optional.ofNullable(b.getCounter()).orElse(0);
                    actionNougat.setCounter(aNum + bNum);
                    return actionNougat;
                }).ifPresent(transitions::add);
            });

//            List<TPrivacyActionNougat> targets = transitions.stream()
//                    .sorted(Comparator.comparing(TPrivacyActionNougat::getSensitive).thenComparing(TPrivacyActionNougat::getCounter).reversed())
//                    .collect(Collectors.toList());

            List<TPrivacyActionNougat> targets = transitions.stream().sorted(Comparator
                    .comparing((Function<TPrivacyActionNougat, Boolean>) n -> Optional.ofNullable(n.getSensitive()).orElse(false))
                    .thenComparing(n -> Optional.ofNullable(n.getCounter()).orElse(0))
                    .reversed())
                    .collect(Collectors.toList());

            sdkVO.setActionNougatList(targets);
        }
    }

    /**
     * 组装sdk存储个人信息
     *
     * @param sdkList
     * @param sharedPrefs
     */
    private void sdkSharedPrefs(List<SdkVO> sdkList, List<TPrivacySharedPrefs> sharedPrefs) {
    	if(sdkList==null) {
    		return;
    	}
        for (SdkVO sdkVO : sdkList) {
            List<TPrivacySharedPrefs> sharedPrefsList = new ArrayList<>();
            if (StringUtils.isEmpty(sdkVO.getPackageName())) {
                continue;
            }
            for (TPrivacySharedPrefs prefs : sharedPrefs) {
                if (prefs.getExecutorType() != 2 || StringUtils.isEmpty(prefs.getExecutor())) {
                    continue;
                }

                // 保存sdkId之后
                String sdkIdsJson = Optional.ofNullable(prefs.getSdkIds()).orElse("[0]");
                List<Long> sdkIds = JSON.parseArray(sdkIdsJson, Long.class);
                // sdk类型
                if (sdkIds.contains(sdkVO.getId())) {
                    sharedPrefsList.add(prefs);
                    continue;
                }

                // 未保存SDK之前
                String[] executors = prefs.getExecutor().split(",");
                List<String> executorList = new ArrayList<>(executors.length);
                Collections.addAll(executorList, executors);
                // sdk类型
                if (executorList.contains(sdkVO.getName())) {
                    sharedPrefsList.add(prefs);
                }
            }
            sdkVO.setSharedPrefs(sharedPrefsList);
        }
    }

    private String generateReport(Map<String, Object> params, String templateName, String outFileName, int fileType) {
        ReportContext reportContext = reportService.generateReportContext(params, templateName, outFileName, fileType);
        ReportManager reportManager = new DefaultReportManagerImpl(reportContext);
        return reportManager.generateReport();
    }

    
    @Override
    public JSONObject getDetectionLawDetail(TTask task,String lawType) {

        JSONObject jsonData = new JSONObject();
        // 164号文
        jsonData.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId((lawType==null ? 1L :Long.valueOf(lawType)), task.getTaskId()));
		return jsonData;
	}
    
    public List<DetectionChildResultDetailVO> getDetectionDetail(String documentId) {
    	return getDetectionDetail(documentId, null);
    }

    public List<DetectionChildResultDetailVO> getHarmonyDetectionDetail(String documentId) {
        return getHarmonyDetectionDetail(documentId, null);
    }

    /**
     * 检测结果详情分析数据拼装
     * @param itemResultStatus 1安全 2存在风险  (过滤取数据)
     * @param documentId
     * @return
     */
    public List<DetectionChildResultDetailVO> getDetectionDetail(String documentId,Integer itemResultStatus) {
        //        List<DetectionResultDetailVO> detectionResultDetailVOList = new ArrayList<DetectionResultDetailVO>();
        TaskDetailVO taskDetailVO = detectionMongodbService.findByDocumentId(documentId);
        // 动态检测项（动态检测项，没有详情列表）
        JSONArray jsonArray = itemCountQuery(taskDetailVO);

        List<DetectionChildResultDetailVO> childList = new ArrayList<DetectionChildResultDetailVO>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject resultObj = jsonArray.getJSONObject(i);
            JSONArray childArray = resultObj.getJSONArray("detection_item");
            //            DetectionResultDetailVO detectionResultDetailVO = new DetectionResultDetailVO();
            //            if (!resultObj.get("_id").equals(null) && !resultObj.get("_id").equals("")) {
            //                TDetectionItemType detectionItemType = new TDetectionItemType();
            //                detectionItemType = detectionItemTypeMapper.selectByPrimaryKey(resultObj.get("_id"));
            //                detectionResultDetailVO.setTypeName(detectionItemType.getTypeName());
            //                detectionResultDetailVO.setTypeId(detectionItemType.getId());
            //            }
            for (int j = 0; j < childArray.size(); j++) {
                JSONObject childJson = childArray.getJSONObject(j);
                DetectionChildResultDetailVO childResultDetailVO = new DetectionChildResultDetailVO();
                if (!childJson.isEmpty() && !childJson.equals(null)) {
                    // 排除掉基本检测项中的（基本信息检测、应用权限检测、应用行为检测、第三方SDK检测、恶意程序检测）
                    String item_id = childJson.getString("detection_item_id");
                    
                    String mark = commonProperties.getProperty("custom.mark");
                    List<String> privacyExcludedItems;
            	    //百胜定制为快餐外卖
            	    if (StringUtils.isNoneBlank(mark) && mark.equals("baisheng")){
                        privacyExcludedItems = BAISHENG_ITEMS;
            	    } else {
                        if (iPrivacyDetectionTransferRiskService.haveResult(taskDetailVO, NEW_ANDROID_ITEMS)) {
                            privacyExcludedItems = NEW_ANDROID_ITEMS;
                        } else {
                            privacyExcludedItems = OLD_ANDROID_ITEMS;
                        }
                    }
                    if (privacyExcludedItems.contains(item_id)) {
                        TDetectionItem detectionItem = detectionItemMapper.findDetectionItemInfo(item_id, TerminalTypeEnum.ANDROID.getValue());
                        if (detectionItem == null) {
                            continue;
                        }
                        // 动态检测项
                        if (Arrays.asList(dynamic_items).contains(item_id)) {
                            dynamicItemDataBuilder(childJson, childResultDetailVO, detectionItem);
                        } else {
                            // 静态检测项
                            staticItemDataBuilder(childJson, childResultDetailVO, detectionItem);
                        }
                        childResultDetailVO.setItemNo(item_id);
                        childList.add(childResultDetailVO);
                    }
                }
            }
            //            detectionResultDetailVO.setChildVOList(childList);
            //            detectionResultDetailVOList.add(detectionResultDetailVO);
        }
        
        if(childList == null || childList.size()==0 || itemResultStatus== null) {
        	return childList;
        }
        
        List<DetectionChildResultDetailVO> newChildList = new ArrayList<DetectionChildResultDetailVO>();
        //1安全 2存在风险  (过滤取数据)
        for (DetectionChildResultDetailVO detectionChildResultDetailVO : childList) {
			if(detectionChildResultDetailVO.getStatus()==itemResultStatus) {
				newChildList.add(detectionChildResultDetailVO);
			}
		}
        return newChildList;
    }

    /**
     * 检测结果详情分析数据拼装
     * @param itemResultStatus 1安全 2存在风险  (过滤取数据)
     * @param documentId
     * @return
     */
    public List<DetectionChildResultDetailVO> getHarmonyDetectionDetail(String documentId,Integer itemResultStatus) {
        TaskDetailVO taskDetailVO = detectionMongodbService.findByDocumentId(documentId);
        // 动态检测项（动态检测项，没有详情列表）
        JSONArray jsonArray = itemCountQuery(taskDetailVO);

        List<DetectionChildResultDetailVO> childList = new ArrayList<DetectionChildResultDetailVO>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject resultObj = jsonArray.getJSONObject(i);
            JSONArray childArray = resultObj.getJSONArray("detection_item");
            for (int j = 0; j < childArray.size(); j++) {
                JSONObject childJson = childArray.getJSONObject(j);
                DetectionChildResultDetailVO childResultDetailVO = new DetectionChildResultDetailVO();
                if (!childJson.isEmpty()) {
                    String itemId = childJson.getString("detection_item_id");
                    TDetectionItem detectionItem = detectionItemMapper.findDetectionItemInfo(itemId, TerminalTypeEnum.HARMONY.getValue());
                    // 排除掉基本检测项中的（基本信息检测、应用权限检测、应用行为检测、第三方SDK检测、恶意程序检测）
                    if (detectionItem == null || BASE_INFO_ITEMS.contains(itemId)) {
                        continue;
                    }
                    // 静态检测项
                    staticItemDataBuilder(childJson, childResultDetailVO, detectionItem);
                    childResultDetailVO.setItemNo(itemId);
                    childList.add(childResultDetailVO);
                }
            }
        }

        if (childList.isEmpty() || itemResultStatus == null) {
            return childList;
        }

        List<DetectionChildResultDetailVO> newChildList = new ArrayList<DetectionChildResultDetailVO>();
        //1安全 2存在风险  (过滤取数据)
        for (DetectionChildResultDetailVO detectionChildResultDetailVO : childList) {
            if(detectionChildResultDetailVO.getStatus()==itemResultStatus) {
                newChildList.add(detectionChildResultDetailVO);
            }
        }
        return newChildList;
    }

    /**
     * 动态检测项报告详情
     *
     * @param childJson
     * @param childResultDetailVO
     * @param detectionItem
     * @return
     */
    private void dynamicItemDataBuilder(JSONObject childJson, DetectionChildResultDetailVO childResultDetailVO, TDetectionItem detectionItem) {
        childResultDetailVO.setIs_dynamic(true);
        BeanUtils.copyProperties(detectionItem, childResultDetailVO);
        childResultDetailVO.setGradeName(detectionItem.getGrade().getName());
        childResultDetailVO.setResult(childJson.getInteger("status") == 2 ? "存在风险" : "安全");
        childResultDetailVO.setStatus(childJson.getInteger("status"));
        childResultDetailVO.setResultDetail(childJson.getString("describe"));
        List<String> fileList = new ArrayList<String>();
        if (!childJson.get("result_content").equals("") && !childJson.get("result_content").equals(null)) {
            String[] array = childJson.getString("result_content").split("\n");
            fileList = Arrays.asList(array);
        }
        childResultDetailVO.setDetail2List(fileList);
        childResultDetailVO.setIs_dynamic(true);
    }

    /**
     * 静态检测项报告详情
     *
     * @param childJson
     * @param childResultDetailVO
     * @param detectionItem
     */
    private void staticItemDataBuilder(JSONObject childJson, DetectionChildResultDetailVO childResultDetailVO, TDetectionItem detectionItem) {
        String item_id = childJson.getString("detection_item_id");
        BeanUtils.copyProperties(detectionItem, childResultDetailVO);
        childResultDetailVO.setGradeName(null == detectionItem.getGrade() ? "N/A" : detectionItem.getGrade().getName());
        Integer status = childJson.getInteger("status");
        childResultDetailVO.setResult(status == 2 ? "存在风险" : "安全");
        childResultDetailVO.setStatus(status);
        childResultDetailVO.setResultDetail(childJson.getString("describe"));
        // 存在风险
        if (status == 2) {
            JSONObject result_content = new JSONObject();
            if (!childJson.get("result_content").equals("") && !childJson.get("result_content").equals(null) && !childJson.get("result_content").equals("[]")) {
                Object resultContentObj = childJson.get("result_content");
                if (resultContentObj instanceof JSONObject) {
                    result_content = (JSONObject) resultContentObj;
                }
                // 滥用、ip检测、境外ip检测
                if (item_id.equals("0204") || item_id.equals("0805") || item_id.equals("0806") || item_id.equals("0807")) {
                    ipAndPermissionAbuseDataBuilder(childJson, childResultDetailVO, result_content);
                }
                //0104   0101  0102  0204  0202  0802
//                0803 0705  0605 0301
                // WebView远程代码执行漏洞 、私有函数调用风险、WebView明文存储密码漏洞、模拟器运行风险、终端ROOT状态检测、全局异常、绕过签名检验漏洞
                else if (item_id.equals("0504") || item_id.equals("0315") || item_id.equals("0503") || item_id.equals("0713") || item_id.equals("0710")
                        || item_id.equals("0511") || item_id.equals("0804") || item_id.equals("0606") || item_id.equals("0319") || item_id.equals("0401")
                        || item_id.equals("0501") || item_id.equals("0607") || item_id.equals("0712")
                        || item_id.equals("0803") || item_id.equals("0705") || item_id.equals("0605") || item_id.equals("0301") || NEW_ANDROID_ITEMS.contains(item_id)
                        || detectionItem.getTerminalType() == TerminalTypeEnum.HARMONY) {
                    specialDataBuilder(resultContentObj, childResultDetailVO, detectionItem);
                }
            }
        }
    }

    /**
     * 权限滥用、ip检测项报告详情
     *
     * @param childJson
     * @param childResultDetailVO
     */
    private void ipAndPermissionAbuseDataBuilder(JSONObject childJson, DetectionChildResultDetailVO childResultDetailVO, JSONObject result_content) {
        String item_id = childJson.getString("detection_item_id");
        // 滥用
        if (item_id.equals("0204")) {
            List<String> fileList = new ArrayList<String>();
            String key = "";
            if (result_content.containsKey("检测到滥用的权限")) {
                key = result_content.getString("检测到滥用的权限");
            } else {
                if (result_content.containsKey("permissionList")) {
                    key = result_content.getString("permissionList");
                }
            }
            fileList = new Gson().fromJson(key, new TypeToken<List<String>>() {
            }.getType());
            childResultDetailVO.setDetail2List(fileList);
            childResultDetailVO.setUnsafe_num(String.valueOf(fileList.size()));
        }
        // ip检测
        if (item_id.equals("0805")) {
            List<String> fileList = new ArrayList<String>();
            JSONArray ipJsonArray = new JSONArray();
            ipJsonArray = result_content.getJSONArray("ip_info");
            if (ipJsonArray != null && ipJsonArray.size() > 0 && !ipJsonArray.isEmpty()) {
                for (int g = 0; g < ipJsonArray.size(); g++) {
                    JSONObject ipObje = ipJsonArray.getJSONObject(g);
                    fileList.add(ipObje.getString("ip"));
                }
                childResultDetailVO.setDetail2List(fileList);
                childResultDetailVO.setUnsafe_num(String.valueOf(fileList.size()));
            }
        }

        // ipv6检测
        if (item_id.equals("0807")) {
            List<String> fileList = new ArrayList<String>();
            JSONArray ipJsonArray = new JSONArray();
            ipJsonArray = result_content.getJSONArray("ip_info");
            if (ipJsonArray != null && ipJsonArray.size() > 0 && !ipJsonArray.isEmpty()) {
                for (int g = 0; g < ipJsonArray.size(); g++) {
                    JSONObject ipObje = ipJsonArray.getJSONObject(g);
                    fileList.add(ipObje.getString("ip"));
                }
                childResultDetailVO.setDetail2List(fileList);
                childResultDetailVO.setUnsafe_num(String.valueOf(fileList.size()));
            }
        }
        // 境外ip
        if (item_id.equals("0806")) {
            List<IpDetailVO> ipList = new ArrayList<IpDetailVO>();
            JSONArray ipJsonArray = new JSONArray();
            ipJsonArray = result_content.getJSONArray("ip_info");
            if (ipJsonArray != null && ipJsonArray.size() > 0 && !ipJsonArray.isEmpty()) {
                for (int k = 0; k < ipJsonArray.size(); k++) {
                    JSONObject ipObje = ipJsonArray.getJSONObject(k);
                    IpDetailVO ipDetailVO = new IpDetailVO();
                    ipDetailVO.setIp(ipObje.getString("ip"));
                    ipDetailVO.setCount(ipObje.getInteger("count"));
                    ipDetailVO.setIpAddress(ipObje.getString("location"));
                    ipList.add(ipDetailVO);
                }
                childResultDetailVO.setIpList(ipList);
                childResultDetailVO.setUnsafe_num(String.valueOf(ipList.size()));
            }
        }
    }

    /**
     * WebView远程代码执行漏洞 、私有函数调用风险、WebView明文存储密码漏洞、模拟器运行风险、终端ROOT状态检测、全局异常
     *
     * @param resultContentObj
     * @param childResultDetailVO
     * @param detectionItem
     */
    private void specialDataBuilder(Object resultContentObj, DetectionChildResultDetailVO childResultDetailVO, TDetectionItem detectionItem) {
        if (resultContentObj instanceof JSONObject) {
            JSONObject result_content = new JSONObject();
            result_content = (JSONObject) resultContentObj;
            otherItemDataBuilder(childResultDetailVO, result_content);
        } else {
            BeanUtils.copyProperties(detectionItem, childResultDetailVO);
            List<String> fileList = new ArrayList<String>();
            fileList.add(resultContentObj.toString());
            childResultDetailVO.setDetail2List(fileList);
            childResultDetailVO.setUnsafe_num("1");
        }
    }

    private void otherItemDataBuilder(DetectionChildResultDetailVO childResultDetailVO, JSONObject result_content) {
        try {
			if (!result_content.toString().contains("file_total_num")) {
			    return;
			}
			childResultDetailVO.setTotal_num(result_content.get("file_total_num") == null ? "0" : String.valueOf(result_content.getInteger("file_total_num")));
			childResultDetailVO.setUnsafe_num(result_content.get("unsafe_num") == null ? "0" : String.valueOf(result_content.getInteger("unsafe_num")));
			// file_unsafe_info 为字符串数组
			if (result_content.containsKey("unsafe_info")) {
			    JSONArray fileJsonArray = result_content.getJSONArray("unsafe_info");
			    if (fileJsonArray != null && fileJsonArray.size() > 0 && !fileJsonArray.isEmpty()) {
			        List<SensitiveWordFilePathVO> filePathList = new ArrayList<SensitiveWordFilePathVO>();
			        List<String> fileList = new ArrayList<String>();
			        for (Object object : fileJsonArray) {
			            if (object instanceof String) {
			                fileList.add(WordStringUtil.converCharacters(object.toString()));
//                        SensitiveWordFilePathVO sensitiveWordFilePathVO = new SensitiveWordFilePathVO();
//                        sensitiveWordFilePathVO.setFilePath(WordStringUtil.converCharacters(WordStringUtil.converCharacters(object.toString())));
//                        filePathList.add(sensitiveWordFilePathVO);
			            } else {
			                JSONObject fileJson = new JSONObject();
			                fileJson = (JSONObject) object;
			                SensitiveWordFilePathVO sensitiveWordFilePathVO = new SensitiveWordFilePathVO();
			                String filePath = WordStringUtil.converCharacters(fileJson.getString("file_path"));
			                String methodName = WordStringUtil.converCharacters(WordStringUtil.delBlank(fileJson.getString("methodName")));
			                String unsafeCode = WordStringUtil.converCharacters(WordStringUtil.delBlank(fileJson.getString("unsafe_code")));
			                sensitiveWordFilePathVO.setFilePath(StringUtils.isNotBlank(methodName) ? filePath + " " + methodName : filePath);
			                sensitiveWordFilePathVO.setCodePath(unsafeCode);
			                filePathList.add(sensitiveWordFilePathVO);
			            }
			        }
			        childResultDetailVO.setDetail2List(fileList);
			        childResultDetailVO.setDetailList(filePathList);
			    }
			}
		} catch (Exception e) {
			LOG.info(e.getMessage());
		}
    }

    private JSONArray itemCountQuery(TaskDetailVO taskDetailVO) {
        JSONArray array = new JSONArray();
        if (taskDetailVO == null) {
            return array;
        }
        if (taskDetailVO.getDetection_result() == null) {
            return array;
        }

        array = JSONArray.parseArray(taskDetailVO.getDetection_result().toString());
        if (array == null || array.size() == 0) {
            // 如果条件满足，则返回一个新的 JSONArray 对象
            return new JSONArray();
        }
        List<String> typeIds = new ArrayList<>();
        Set<String> anyMatch = new HashSet<>();
        for (int i = 0; i < array.size(); i++) {
            JSONObject childObj = array.getJSONObject(i);
            String type_id = childObj.get("type_id") == null ? "" : childObj.getString("type_id");
            if (org.apache.commons.lang3.StringUtils.isNoneBlank(type_id) && anyMatch.add(type_id)) {
                typeIds.add(type_id);
            }
        }
        JSONArray itemCountQuery = new JSONArray();
        for (int i = 0; i < typeIds.size(); i++) {
            String type_id = typeIds.get(i);
            JSONArray item_array = getItems(array, type_id);
            JSONObject obj = new JSONObject();
            obj.put("_id", type_id);
            obj.put("detection_item", item_array);
            itemCountQuery.add(obj);
        }
        return itemCountQuery;
    }

    private JSONArray getItems(JSONArray array, String type_id) {
        JSONArray newArray = new JSONArray();
        for (int i = 0; i < array.size(); i++) {
            JSONObject childObj = array.getJSONObject(i);
            try {
                String child_type = childObj.getString("type_id");
                if (!type_id.equals(child_type)) {
                    continue;
                }
                newArray.add(childObj);
            } catch (Exception e) {
                continue;
            }

        }
        return newArray;
    }

    /**
     * 获取XML声明权限
     *
     * @param documentId mongo id
     * @return 权限列表
     */
    @Override
    public List<String> getDetectionPermissions(String documentId) {
        TaskDetailVO taskDetailVO = mongodbService.findByDocumentId(documentId);
        if (taskDetailVO == null || CollectionUtils.isEmpty(taskDetailVO.getDetection_result())) {
            return new ArrayList<>();
        }
        List<String> detectionPermissions = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(taskDetailVO.getDetection_result().toString());
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {

                JSONObject json = jsonArray.getJSONObject(i);
                if (json.get("detection_item_id") != null && (json.get("detection_item_id").equals("0102") || json.get("detection_item_id").equals("0202"))) {
                    if (json.get("result_content") != null && !json.get("result_content").equals("") && json.get("result_content") instanceof JSONObject) {
                        try {
                            JSONObject childJson = json.getJSONObject("result_content");
                            detectionPermissions.addAll(childJson.getJSONArray("permissionList").toJavaList(String.class));
                        } catch (Exception ex) {
                            ex.getMessage();
                            LOG.info("result_content不是数组:" + json.toJSONString());
                        }
                    }
                }
            }
        }

        Set<String> set = new HashSet<>(detectionPermissions);
        return new ArrayList<>(set);
    }

    @Override
    public SafeDetailVO getSafeDetail(String documentId, Integer behaviorStage) {
        TTask tTask = taskMapper.findByDocumentId(documentId);
        // 声明权限
        List<PermissionVO> vo1 = this.getXMLPermission(tTask, behaviorStage);
        // 敏感权限
        List<PermissionVO> vo2 = this.getSensitivePermission(tTask, behaviorStage);
        // 尝试使用未申明权限
        List<PermissionVO> vo3 = this.getNoDeclaredPermission(tTask, behaviorStage);
        // 权限过度申请
        List<PermissionVO> vo4 = this.getExcessPermission(tTask, behaviorStage);

        List<List<PermissionVO>> list = new LinkedList<>();
        list.add(vo1);
        list.add(vo2);
        list.add(vo3);
        list.add(vo4);

        SafeDetailVO safeDetailVO = new SafeDetailVO();
        safeDetailVO.setList(list);
        safeDetailVO.setTaskId(tTask.getTaskId());
        return safeDetailVO;
    }

    @Override
    public List<String> getBehaviorSdk(String documentId, Integer behaviorStage) {
        TTask task = taskService.findByDocumentId(documentId);
        return privacyActionNougatService.getBehaviorSdk(task.getTaskId(), behaviorStage);
    }

    @Override
    public List<String> getLawDetailBehaviorSdk(String documentId, Integer dataType, String itemNo) {
        TTask task = taskService.findByDocumentId(documentId);
        return privacyActionNougatService.getLawDetailBehaviorSdk(task.getTaskId(), dataType, itemNo);
    }

    @Override
    public List<DetectResultVO> getDetectResults(String documentId, Long userId) throws IjiamiApplicationException {
        List<DetectResultVO> results = new ArrayList<>();
        Example example = new Example(TTask.class);
        Example.Criteria criteria = example.createCriteria();
        if (documentId != null) {
            criteria.andEqualTo("apkDetectionDetailId", documentId);
        }
        if (userId != null) {
            criteria.andEqualTo("createUserId", userId);
        }
        criteria.andEqualTo("detectComplete", 1);
        List<TTask> tasks = taskMapper.selectByExample(example);
        for (TTask task : tasks) {
            results.add(getDetectResult(documentId));
        }
        return results;
    }

    @Override
    public BigDataConnectVO getBigDataConnect(String documentId) throws IjiamiApplicationException {
        BigDataConnectVO bigDataConnect = new BigDataConnectVO();
        TTask task = taskMapper.findByDocumentId(documentId);
        BaseMessageVO baseMessageVO = taskService.getBaseMessage(task.getApkDetectionDetailId());
        bigDataConnect.setAppBaseInfo(baseMessageVO);
        bigDataConnect.setSdks(taskService.getSdkList(task.getApkDetectionDetailId(), task.getTaskId()));
        bigDataConnect.setLawType(privacyCheckService.countType(task.getTaskId()));
        bigDataConnect.setPrivacyChecks(privacyCheckService.selectPrivacyPolicy(task.getTaskId(), null, task.getTerminalType().getValue()));
        bigDataConnect.setXmlPermissions(this.getXMLPermission(task, null));
        bigDataConnect.setExcessPermissions(this.getExcessPermission(task, null));
        bigDataConnect.setNoDeclaredPermissions(this.getNoDeclaredPermission(task, null));
        bigDataConnect.setSensitivePermissions(this.getSensitivePermission(task, null));
        bigDataConnect.setPrivacyActions(privacyActionService.findActionsByTaskId(task.getTaskId()));
        bigDataConnect.setPrivacyActionNougats(privacyActionNougatService.findAllByTaskId(task.getTaskId()));
        bigDataConnect.setPrivacySensitiveWords(privacySensitiveWordService.findByTaskId(task.getTaskId(), 0));
        bigDataConnect.setCookies(privacySensitiveWordService.findByCookie(task.getTaskId(), 0));
        bigDataConnect.setOutSideAddress(privacyOutsideAddressService.getOutSideAddress(task.getTaskId(), 0));
        bigDataConnect.setPolicyImgs(privacyPolicyImgService.findPolicyImgsByTaskId(task.getTaskId()));
        bigDataConnect.setSensitiveWordImgs(privacySensitiveWordService.findImgByTaskId(task.getTaskId(), 0));
        // 新增部分
        bigDataConnect.setId(documentId);
        bigDataConnect.setMd5(baseMessageVO.getApkMd5());
        bigDataConnect.setIsRisk(task.getIsSafe() ? 0 : 1);
        bigDataConnect.setDetectionTime(task.getTaskEndtime());
        return bigDataConnect;
    }

    @Override
    public DetectResultVO getDetectResult(String documentId) throws IjiamiApplicationException {
        DetectResultVO detectResultVO = new DetectResultVO();
        TTask task = taskMapper.findByDocumentId(documentId);
        detectResultVO.setAppBaseInfo(taskService.getBaseMessage(task.getApkDetectionDetailId()));
        detectResultVO.setSdks(taskService.getSdkList(task.getApkDetectionDetailId(), task.getTaskId()));
        detectResultVO.setLawType(privacyCheckService.countType(task.getTaskId()));
        detectResultVO.setPrivacyChecks(privacyCheckService.selectPrivacyPolicy(task.getTaskId(), null, task.getTerminalType().getValue()));
        detectResultVO.setXmlPermissions(this.getXMLPermission(task, null));
        detectResultVO.setExcessPermissions(this.getExcessPermission(task, null));
        detectResultVO.setNoDeclaredPermissions(this.getNoDeclaredPermission(task, null));
        detectResultVO.setSensitivePermissions(this.getSensitivePermission(task, null));
        detectResultVO.setPrivacyActions(privacyActionService.findActionsByTaskId(task.getTaskId()));
        detectResultVO.setPrivacyActionNougats(privacyActionNougatService.findAllByTaskId(task.getTaskId()));
        detectResultVO.setPrivacySensitiveWords(privacySensitiveWordService.findByTaskId(task.getTaskId(), 0));
        detectResultVO.setCookies(privacySensitiveWordService.findByCookie(task.getTaskId(), 0));
        detectResultVO.setOutSideAddress(privacyOutsideAddressService.getOutSideAddress(task.getTaskId(), 0));
        detectResultVO.setPolicyImgs(privacyPolicyImgService.findPolicyImgsByTaskId(task.getTaskId()));
        detectResultVO.setSensitiveWordImgs(privacySensitiveWordService.findImgByTaskId(task.getTaskId(), 0));
        return detectResultVO;
    }

    /**
     * 统计声明权限
     *
     * @param task 任务数据
     * @return 统计数据
     */
    private CountPermissionVO countXmlPermission(TTask task) {
        CountPermissionVO countPermissionVO = new CountPermissionVO();
        List<PermissionVO> xmlPermission = getXMLPermission(task, 0);
        if (CollectionUtils.isEmpty(xmlPermission)) {
//            countPermissionVO.setPieChartImageBase64(getPieChartImageBase64("声明权限-是否与个人信息相关", 0, "是", 0, "否"));
            return countPermissionVO;
        }
        List<PermissionVO> privacy = xmlPermission.stream()
                .filter(permissionVO -> permissionVO.getIsPrivacy() == PrivacyStatusEnum.YES.getValue())
                .collect(Collectors.toList());
        List<PermissionVO> risk = xmlPermission.stream()
                .filter(permissionVO -> permissionVO.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value).
                collect(Collectors.toList());
        List<PermissionVO> used = xmlPermission.stream().filter(permissionVO -> permissionVO.getUseCount() > 0).collect(Collectors.toList());
        countPermissionVO.setPermissions(xmlPermission);
        countPermissionVO.setAllSum(xmlPermission.size());
        countPermissionVO.setPrivacySum(privacy.size());
        countPermissionVO.setRiskSum(risk.size());
        countPermissionVO.setUsedSum(used.size());
//        countPermissionVO.setPieChartImageBase64(getPieChartImageBase64("声明权限-是否与个人信息相关", countPermissionVO.getPrivacySum(), "是",
//                countPermissionVO.getAllSum() - countPermissionVO.getPrivacySum(), "否"));
        return countPermissionVO;
    }

    /**
     * 统计尝试使用未声明权限
     *
     * @param task 任务
     * @return 权限列表
     */
    private CountPermissionVO countNoDeclaredPermission(TTask task) {
        CountPermissionVO countPermissionVO = new CountPermissionVO();
        List<PermissionVO> countNoDeclaredPermission = new ArrayList<>();
        List<PermissionVO> permissionVOS = privacyActionNougatService.countActionPermissions(task.getTaskId());
        List<String> xmlPermission = getDetectionPermissions(task.getApkDetectionDetailId());
        for (PermissionVO permissionVO : permissionVOS) {
            if (!xmlPermission.contains(permissionVO.getName())
                    && Objects.nonNull(permissionVO.getName())
                    && Objects.nonNull(permissionVO.getIsPrivacy())
                    && Objects.nonNull(permissionVO.getType())) {
                countNoDeclaredPermission.add(permissionVO);
            }
        }
        if (CollectionUtils.isEmpty(countNoDeclaredPermission)) {
            return countPermissionVO;
        }
        List<PermissionVO> collect = new ArrayList<>();
        Map<String, PermissionVO> tmp = new HashMap<>();
        for (PermissionVO item : countNoDeclaredPermission) {
            if (!tmp.containsKey(item.getName())) {
                tmp.put(item.getName(), item);
            } else {
                PermissionVO lastVO = tmp.get(item.getName());
                lastVO.setSdkCount(lastVO.getSdkCount() + item.getSdkCount());
                lastVO.setAppCount(lastVO.getAppCount() + item.getAppCount());
                lastVO.setUseCount(lastVO.getUseCount() + item.getUseCount());
            }
        }
        for (Map.Entry<String, PermissionVO> entry : tmp.entrySet()) {
            collect.add(entry.getValue());
        }
        List<PermissionVO> privacy = collect.stream().filter(permissionVO -> permissionVO.getIsPrivacy() == PrivacyStatusEnum.YES.getValue()).collect(Collectors.toList());
        List<PermissionVO> risk = collect.stream().filter(permissionVO -> permissionVO.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value).collect(Collectors.toList());
        List<PermissionVO> filterList = filterCountSensitivePermission(collect, countNoDeclaredPermission);

        countPermissionVO.setPermissions(filterList);
        countPermissionVO.setAllSum(collect.size());
        countPermissionVO.setPrivacySum(privacy.size());
        countPermissionVO.setRiskSum(risk.size());
        return countPermissionVO;
    }

    /**
     * 统计敏感权限
     *
     * @param
     * @return
     */
    private CountPermissionVO countSensitivePermission(TTask task) {
        CountPermissionVO countPermissionVO = new CountPermissionVO();
        List<PermissionVO> permissionVOS = privacyActionNougatService.countActionPermissions(task.getTaskId())
                .stream()
                .filter(permissionVO -> Objects.nonNull(permissionVO.getName()))
                .filter(permissionVO -> Objects.nonNull(permissionVO.getType()))
                .collect(Collectors.toList());
        List<PermissionVO> countSensitivePermission = permissionVOS.stream()
                .filter(permissionVO -> permissionVO.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value)
                .collect(Collectors.toList());
        List<PermissionVO> sensitivePermission = getSensitivePermission(task, null);
        for (PermissionVO permissionVO : sensitivePermission) {
            boolean anyMatch = permissionVOS.stream().anyMatch(p -> p.getName().equals(permissionVO.getName()));
            if (!anyMatch) {
                countSensitivePermission.add(permissionVO);
            }
        }

        if (CollectionUtils.isEmpty(countSensitivePermission)) {
//            countPermissionVO.setPieChartImageBase64(getPieChartImageBase64("敏感权限-是否与个人信息相关", 0, "是", 0, "否"));
            return countPermissionVO;
        }
        Collections.sort(countSensitivePermission);

        List<PermissionVO> collect = new ArrayList<>();
        Map<String, PermissionVO> tmp = new HashMap<>();
        for (PermissionVO item : countSensitivePermission) {
            if (!tmp.containsKey(item.getName())) {
                tmp.put(item.getName(), item);
            } else {
                PermissionVO lastVO = tmp.get(item.getName());
                lastVO.setSdkCount(lastVO.getSdkCount() + item.getSdkCount());
                lastVO.setAppCount(lastVO.getAppCount() + item.getAppCount());
                lastVO.setUseCount(lastVO.getUseCount() + item.getUseCount());
            }
        }
        for (Map.Entry<String, PermissionVO> entry : tmp.entrySet()) {
            collect.add(entry.getValue());
        }

        //        List<PermissionVO> collect = countSensitivePermission.stream().collect(
        //                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PermissionVO::getName))), ArrayList::new));
        List<PermissionVO> privacy = collect.stream().filter(permissionVO -> permissionVO.getIsPrivacy() == PrivacyStatusEnum.YES.getValue()).collect(Collectors.toList());

        List<PermissionVO> filterList = filterCountSensitivePermission(collect, countSensitivePermission);
        countPermissionVO.setPermissions(filterList);
        countPermissionVO.setAllSum(collect.size());
        countPermissionVO.setPrivacySum(privacy.size());
//        countPermissionVO.setPieChartImageBase64(getPieChartImageBase64("敏感权限-是否与个人信息相关", countPermissionVO.getPrivacySum(), "是",
//                countPermissionVO.getAllSum() - countPermissionVO.getPrivacySum(), "否"));

        return countPermissionVO;
    }

    private List<PermissionVO> filterCountSensitivePermission(List<PermissionVO> collect, List<PermissionVO> voList) {
        if (voList == null || voList.size() == 0) {
            return voList;
        }
        List<PermissionVO> list = new ArrayList<>();
        for (PermissionVO permissionVO : collect) {
            for (PermissionVO permission : voList) {
                if (permission.getName().equals(permissionVO.getName()) && permission.getExecutorType() == 1) { //主体类型 1APP
                    permissionVO.setAppIsPrivacy(permission.getIsPrivacy());
                }
                if (permission.getName().equals(permissionVO.getName()) && permission.getExecutorType() == 2) { //主体类型 2SDK
                    permissionVO.setSdkIsPrivacy(permission.getIsPrivacy());
                }
            }
            list.add(permissionVO);
        }
        return list;
    }

    private List<TPrivacyActionNougat> filterCountSensitivePrivacyActionNougat(List<TPrivacyActionNougat> collect, List<TPrivacyActionNougat> voList) {
        if (voList == null || voList.size() == 0) {
            return voList;
        }
        List<TPrivacyActionNougat> list = new ArrayList<>();
        for (TPrivacyActionNougat permissionVO : collect) {
            permissionVO.setAppCounter(0);
            permissionVO.setAppSensitive(false);
            permissionVO.setSdkCounter(0);
            permissionVO.setSdkSensitive(false);
            for (TPrivacyActionNougat permission : voList) {
                if (permission.getActionId().intValue() == permissionVO.getActionId().intValue() && permission.getExecutorType().intValue() == 1) { //主体类型 1APP
                    permissionVO.setAppSensitive(permission.getSensitive());
                    permissionVO.setAppCounter(permission.getCounter() == null ? 0 : (permission.getCounter()+permissionVO.getAppCounter()));
                }
                if (permission.getActionId().intValue() == permissionVO.getActionId().intValue() && permission.getExecutorType().intValue() == 2) { //主体类型 2SDK
                    permissionVO.setSdkSensitive(permission.getSensitive());
                    permissionVO.setSdkCounter(permission.getCounter() == null ? 0 : (permission.getCounter()+permissionVO.getSdkCounter()));
                }
            }
            list.add(permissionVO);
        }
        return list;
    }

    /**
     * 权限过度申请
     *
     * @param
     * @return
     */
    private CountPermissionVO countExcessPermission(TTask task, int behavior) {
        CountPermissionVO countPermissionVO = new CountPermissionVO();

        // 获取过度的权限
        List<PermissionVO> excessPermission = getExcessPermission(task, behavior);
        if (CollectionUtils.isEmpty(excessPermission)) {
            return countPermissionVO;
        }
        List<PermissionVO> collect = new ArrayList<>();
        Map<String, PermissionVO> tmp = new HashMap<>();
        for (PermissionVO item : excessPermission) {
            if (!tmp.containsKey(item.getName())) {
                tmp.put(item.getName(), item);
            } else {
                PermissionVO lastVO = tmp.get(item.getName());
                lastVO.setSdkCount(lastVO.getSdkCount() + item.getSdkCount());
                lastVO.setAppCount(lastVO.getAppCount() + item.getAppCount());
                lastVO.setUseCount(lastVO.getUseCount() + item.getUseCount());
            }
        }
        for (Map.Entry<String, PermissionVO> entry : tmp.entrySet()) {
            collect.add(entry.getValue());
        }

        //        List<PermissionVO> collect = excessPermission.stream().collect(
        //                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PermissionVO::getName))), ArrayList::new));
        List<PermissionVO> privacy = collect.stream().filter(permissionVO -> permissionVO.getIsPrivacy() == PrivacyStatusEnum.YES.getValue()).collect(Collectors.toList());
        List<PermissionVO> risk = collect.stream().filter(permissionVO -> permissionVO.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value).collect(Collectors.toList());
        List<PermissionVO> used = collect.stream().filter(permissionVO -> permissionVO.getUseCount() > 0).collect(Collectors.toList());

        List<PermissionVO> filterList = filterCountSensitivePermission(collect, excessPermission);
        countPermissionVO.setPermissions(filterList);
        countPermissionVO.setAllSum(collect.size());
        countPermissionVO.setPrivacySum(privacy.size());
        countPermissionVO.setRiskSum(risk.size());
        countPermissionVO.setUsedSum(used.size());
//        countPermissionVO.setPieChartImageBase64(getPieChartImageBase64("越权权限-是否与个人信息相关", countPermissionVO.getPrivacySum(), "是",
//                countPermissionVO.getAllSum() - countPermissionVO.getPrivacySum(), "否"));
        return countPermissionVO;
    }

    private CountOutsideAddressVO countOutsideAddress(Long taskId, int behavior) {

        CountOutsideAddressVO countOutsideAddressVO = new CountOutsideAddressVO();
        List<TPrivacyOutsideAddress> outSideAddress = privacyOutsideAddressService.getOutSideAddress(taskId, behavior);
        if (CollectionUtils.isEmpty(outSideAddress)) {
//            countOutsideAddressVO.setPieChartImageBase64(getPieChartImageBase64("通讯行为分析P", 0, "境外", 0, "境内"));
            return countOutsideAddressVO;
        }
        // 兼容检测结果解析前数据
        try {
            outSideAddress.forEach(o -> {
                String stackInfo = o.getStackInfo();
                if (StringUtils.isNotBlank(stackInfo) && DataHandleUtil.isJSONValid(stackInfo)) {
                    List<ActionStackBO> stackBOS = JSON.parseArray(stackInfo, ActionStackBO.class);
                    o.setCounter(stackBOS.size());
                }
            });
        } catch (Exception e) {
            LOG.info("报告数据，通讯行为分析兼容数据处理异常：{}", e.getMessage());
        }
        outSideAddress.forEach(this::removeNoACIIChar);
        List<TPrivacyOutsideAddress> collect = outSideAddress.stream().filter(t -> t.getOutside() == PrivacyStatusEnum.YES.getValue()).collect(Collectors.toList());
        countOutsideAddressVO.setOutsideAddresses(outSideAddress);
        countOutsideAddressVO.setAllSum(outSideAddress.size());
        countOutsideAddressVO.setOutsideSum(collect.size());
//        countOutsideAddressVO.setPieChartImageBase64(getPieChartImageBase64("通讯行为分析", countOutsideAddressVO.getOutsideSum(), "境外",
//                countOutsideAddressVO.getAllSum() - countOutsideAddressVO.getOutsideSum(), "境内"));
        return countOutsideAddressVO;
    }

    private CountActionNougatVO countActionNougat(Long taskId, int behavior) {
        CountActionNougatVO countActionNougatVO = new CountActionNougatVO();
        List<TPrivacyActionNougat> privacyActionNougats = privacyActionNougatService.countActionByTaskId(taskId, behavior);
        if (CollectionUtils.isEmpty(privacyActionNougats)) {
//            countActionNougatVO.setPieChartImageBase64(getPieChartImageBase64("应用行为分析", 0, "敏感", 0, "正常"));
            return countActionNougatVO;
        }
        List<TPrivacyActionNougat> collect = privacyActionNougats.stream().collect(Collectors
                .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TPrivacyActionNougat::getActionId))), ArrayList::new));
//        List<TPrivacyActionNougat> risk = collect.stream().filter(TPrivacyActionNougat::getSensitive).collect(Collectors.toList());
        List<TPrivacyActionNougat> risk = collect.stream().filter(a -> a.getSensitive() == null ? false : a.getSensitive()).collect(Collectors.toList());
        List<TPrivacyActionNougat> filterList = filterCountSensitivePrivacyActionNougat(collect, privacyActionNougats);
        countActionNougatVO.setPrivacyActionNougats(filterList);
        countActionNougatVO.setAllSum(collect.size());
        countActionNougatVO.setRiskSum(risk.size());
//        countActionNougatVO.setPieChartImageBase64(
//                getPieChartImageBase64("应用行为分析", countActionNougatVO.getRiskSum(), "敏感", countActionNougatVO.getAllSum() - countActionNougatVO.getRiskSum(),
//                        "正常"));
        return countActionNougatVO;
    }

    private CountSensitiveWordVO countSensitiveWord(Long taskId, int behavior) {
        CountSensitiveWordVO countSensitiveWordVO = new CountSensitiveWordVO();
        List<TPrivacySensitiveWord> sensitiveWords = privacySensitiveWordService.findByTaskId(taskId, behavior);
        if (CollectionUtils.isEmpty(sensitiveWords)) {
//            countSensitiveWordVO.setPieChartImageBase64(getPieChartImageBase64("个人信息", 0, "SDK", 0, "APP"));
            return countSensitiveWordVO;
        }
        List<TPrivacySensitiveWord> collect = sensitiveWords.stream().filter(s -> s.getExecutorType() == 2).collect(Collectors.toList());
        countSensitiveWordVO.setAllSum(sensitiveWords.size());
        countSensitiveWordVO.setSdkSum(collect.size());
        countSensitiveWordVO.setAppSum(sensitiveWords.size() - collect.size());
//        countSensitiveWordVO
//                .setPieChartImageBase64(getPieChartImageBase64("个人信息", countSensitiveWordVO.getSdkSum(), "SDK", countSensitiveWordVO.getAppSum(), "APP"));
        return countSensitiveWordVO;
    }

    private String getPieChartImageBase64(String title, int yes, String yesName, int no, String noName) {
        JSONArray seriesData = new JSONArray();
        JSONObject series1 = new JSONObject();
        series1.put("name", yesName);
        series1.put("value", yes);
        series1.put("selected", true);
        seriesData.add(series1);

        JSONObject series2 = new JSONObject();
        series2.put("name", noName);
        series2.put("value", no);
        seriesData.add(series2);

        JSONArray legendData = new JSONArray();
        legendData.addAll(Arrays.asList(yesName, noName));

        JSONArray colorData = new JSONArray();
        colorData.addAll(Arrays.asList("#FFB72E", "#009DEB"));

        String options = getPieCharBase64(title, legendData, seriesData, colorData);
        String chart = reportChartService.createChart(options, UuidUtil.uuid() + ".png");
        return reportChartService.getImageBASE64(chart);
    }

    /**
     * 获取饼状图base64
     *
     * @return
     */
    private static String getPieCharBase64(String title, JSONArray legendData, JSONArray seriesData, JSONArray colorData) {
        JSONObject option = new JSONObject();

        JSONObject titleJson = JSONObject.parseObject("{\"text\":\"" + title + "\",\"x\":\"center\",\"textStyle\":{\"fontSize\":30}}");
        JSONObject legendJson = JSON.parseObject(
                "{\"orient\":\"horizontal\",\"left\":\"center\",\"top\":\"90%\",\"itemWidth\":50,\"itemHeight\":28,\"textStyle\":{\"fontSize\":30}}");
        legendJson.put("data", legendData);

        JSONArray seriesArray = new JSONArray();
        JSONObject childJson = JSONObject.parseObject("{\"name\":\"" + title
                + "\",\"type\":\"pie\",\"radius\":\"70%\",\"center\":[\"50%\",\"50%\"],\"label\":{\"normal\":{\"show\":true,\"position\":\"inside\",\"formatter\":\"{c|{c}}\",\"rich\":{\"c\":{\"color\":\"#FFFFFF\",\"fontSize\":30}}}}}");
        childJson.put("data", seriesData);
        seriesArray.add(childJson);

        option.put("title", titleJson);
        option.put("legend", legendJson);
        option.put("color", colorData);
        option.put("series", seriesArray);
        option.put("animation", false);
        return option.toString();
    }

    public static void main(String[] args) {
        String title = "声明权限-是否与个人信息有关";

        JSONArray seriesData = new JSONArray();
        JSONObject series1 = new JSONObject();
        series1.put("name", "是");
        series1.put("value", 3);
        series1.put("selected", true);
        seriesData.add(series1);

        JSONObject series2 = new JSONObject();
        series2.put("name", "否");
        series2.put("value", 10);
        seriesData.add(series2);

        JSONArray legendData = new JSONArray();
        legendData.addAll(Arrays.asList("是", "否"));

        JSONArray colorData = new JSONArray();
        colorData.addAll(Arrays.asList("#1874CD", "#EE7600"));

        String options = getPieCharBase64(title, legendData, seriesData, colorData);
        options = options.replace("\"", "\\\"").replace(" ", "");
        System.out.println(PhantomJSUtils.execPhantomJS(options, "123.png", "E:/zywa/ijiami/report/PhantomJS"));
    }

    private final IPrivacyDetectionTransferRiskService iPrivacyDetectionTransferRiskService;

    @Override
    public DetectionResultVO getDetectionResultV1(String documentId) throws IjiamiApplicationException {

    	 DetectionResultVO vo = new DetectionResultVO();

         TTask task = taskMapper.findByDocumentId(documentId);

         if (task == null) {
             throw new IjiamiApplicationException("检测任务不存在");
         }

         //基本信息
         BaseMessageVO baseMessageVO = taskService.getBaseMessage(task.getApkDetectionDetailId());
         vo.setBaseInfo(baseMessageVO);

         //权限信息
         List<PermissionVO> permissionInfo = analyzePermissionBehaviors(task, null);
         vo.setPermissionInfo(permissionInfo);

         //SDK信息
         List<SdkVO> sdkInfo = getSDKList(documentId, task.getTaskId());
         vo.setSdkInfo(sdkInfo);

         //个人信息风险
        PersonalInfoRiskDetailVO personalRisks = iPrivacyDetectionTransferRiskService.getPersonalInfoRiskDetail(documentId);
         if(personalRisks != null && personalRisks.getList() != null) {
             vo.setPersonalRisks(personalRisks.getList());
         }

         //合规风险
         List<TPrivacyPolicyResult> complianceRisks = iPrivacyDetectionTransferRiskService.getLawInfoRiskDetail(documentId);
         vo.setComplianceRisks(complianceRisks);

         //应用行为数据文件地址（fastDFS地址）appBehaviorMap存储
         String fast_url = getBehaviorDataFile(task);
         if (StringUtils.isNoneBlank(fast_url) && fast_url.contains("group")) {
             String prex = commonProperties.getProperty("detection.result.url.prefix");
             vo.setBehaviorDataFile(prex + fast_url);
         } else {
             vo.setBehaviorDataFile(fast_url);
         }

         // 新增部分
         vo.setId(documentId);
         vo.setMd5(baseMessageVO == null ? null : baseMessageVO.getApkMd5());
         vo.setIsRisk(task.getIsSafe() ? 0 : 1);
         vo.setDetectionTime(task.getTaskEndtime());
         return vo;
    }

    private final SingleFastDfsFileService singleFastDfsFileService;

    //组装个人信息行为数据
    private String getBehaviorDataFile(TTask task) {
    	
    	TTaskExtendVO extendVO = taskExtendMapper.findTaskByTaskId(task.getTaskId());
    	if(extendVO != null && StringUtils.isNotBlank(extendVO.getDownloadUrl()) && extendVO.getDownloadUrl().toLowerCase().endsWith(".txt")) {
    		return extendVO.getDownloadUrl();
    	}
    	
        String path = commonProperties.getProperty("detection.tools.dynamic_path") + task.getTaskId() + ".txt";
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED && new File(path).exists()) {
            FileVO fileVO = uploadFile(task.getTaskId(), path);
            updateDownloadActionUrl(extendVO, fileVO.getFileUrl());
            return fileVO.getFileUrl();
        } else {
            if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            	
//            	String pushData35273 = commonProperties.getProperty("push.data.35237");
            	
            	
                Map<String, Object> map = new HashMap<>();
                //授权前行为
                map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_GRANT.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_GRANT));
                //前台行为
                map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_FRONT.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_FRONT));
                //后台行为
                map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_GROUND.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_GROUND));
              //退出行为
                map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_EXIT.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_EXIT));
                //法规内容
                setLawData(task, map);
                
//                if (task.getTerminalType() == TerminalTypeEnum.IOS) {
//                    // 164号文
//                    map.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(3L, task.getTaskId()));
//                    map.put("miitLawDetectResultMap", miitDetectService.findAllItemByTaskId(task.getTaskId(),PrivacyLawId.IOS_LAW_164.id));
//                
//                	 //35273号文
//                    map.put("miitLawInfo35273", miitDetectService.findLawDetectResultByTaskId(5L, task.getTaskId()));
//                    map.put("miitLawDetectResultMap35273", miitDetectService.findAllItemByTaskId(task.getTaskId(),PrivacyLawId.IOS_LAW_35273.id));
//                } else {
//                    // 164号文
//                    map.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(1L, task.getTaskId()));
//                    map.put("miitLawDetectResultMap", miitDetectService.findAllItemByTaskId(task.getTaskId(),PrivacyLawId.ANDROID_LAW_164.id));
//                    
//                    //191号文
//                    map.put("miitLawInfo191", miitDetectService.findLawDetectResultByTaskId(2L, task.getTaskId()));
//                    map.put("miitLawDetectResultMap191", miitDetectService.findAllItemByTaskId(task.getTaskId(),PrivacyLawId.ANDROID_LAW_191.id));
//                    
//                	//35273号文
//                	map.put("miitLawInfo35273", miitDetectService.findLawDetectResultByTaskId(4L, task.getTaskId()));
//                	map.put("miitLawDetectResultMap35273", miitDetectService.findAllItemByTaskId(task.getTaskId(),PrivacyLawId.ANDROID_LAW_35273.id));
//                }
                
//                Text2Pic.writeTxt(path, JSONObject.toJSONString(map));
                String body = "error";
                try {
                	com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//                    body = mapper.writeValueAsString(map);
                    body = MapToJsonBatchtUtil.mapToJsonBatch(map , 1000);
                } catch (Exception e) {
                    e.getMessage();
                    body = JSONObject.toJSONString(map);
                }
                
                Text2Pic.writeTxt(path, body);
                FileVO fileVO = uploadFile(task.getTaskId(), path);
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
                updateDownloadActionUrl(extendVO, fileVO.getFileUrl());
                return fileVO.getFileUrl();
            }
        }
        updateDownloadActionUrl(extendVO, path);
        return path;
    }
    
    //更新行为数据
    private void updateDownloadActionUrl(TTaskExtendVO extendVO, String path){
    	if(StringUtils.isBlank(path)) {
    		return;
    	}
    	TTaskExtend ex = new TTaskExtend();
    	ex.setId(extendVO.getId());
    	ex.setDownloadUrl(path);
    	taskExtendMapper.updateByPrimaryKeySelective(ex);
    }

    private void setLawData(TTask task, Map<String, Object> map){
    	
    	Integer detectionType = task.getDetectionType();// 1快速
    	//2深度
    	if(detectionType != null && detectionType == 2) {
    		List<TPrivacyPolicyTypeVO> list = new ArrayList<>();
    		List<TPrivacyPolicyType> tPrivacyPolicyTypes = privacyCheckService.countLaw(task.getTaskId(), task.getTerminalType().getValue());
    		if(tPrivacyPolicyTypes == null || tPrivacyPolicyTypes.size()==0) {
    			map.put("lawDepthData", list);
                return;
    		}
			for (TPrivacyPolicyType tPrivacyPolicyType : tPrivacyPolicyTypes) {
				List<PrivacyPolicyTypeVO> vo = privacyCheckService.findByTaskIdAndType(task.getTaskId(), tPrivacyPolicyType.getType(), task.getTerminalType().getValue());

				if(vo == null || vo.size()==0) {
					continue;
				}

				List<PrivacyPolicyTypeAPIVO> policyTypeAPIVOList = new ArrayList<>();

				for (PrivacyPolicyTypeVO privacyPolicyTypeVO : vo) {
					List<PrivacyCheckVO> checkList = privacyPolicyTypeVO.getPrivacyCheckList();
					if(checkList == null || checkList.size() == 0) {
						continue;
					}

					PrivacyPolicyTypeAPIVO newVO = new PrivacyPolicyTypeAPIVO();
					newVO.setPolicyPointNum(privacyPolicyTypeVO.getPolicyPointNum());
					newVO.setTypeName(privacyPolicyTypeVO.getTypeName());
					newVO.setTypeSort(privacyPolicyTypeVO.getTypeSort());

					List<PrivacyCheckAPIVO> chekcAPIList = new ArrayList<>();
					for (PrivacyCheckVO privacyCheckVO : checkList) {
						List<String> imgIds = setImgBase64(privacyCheckVO.getImages());
						privacyCheckVO.setImages(imgIds);

						PrivacyCheckAPIVO api = new PrivacyCheckAPIVO();
						try {
							BeanUtils.copyProperties(privacyCheckVO, api);
						} catch (BeansException e) {
							LOG.error("setLawData", e);
						}
						chekcAPIList.add(api);
					}

					newVO.setPrivacyCheckList(chekcAPIList);
					policyTypeAPIVOList.add(newVO);
				}

				TPrivacyPolicyTypeVO data = new TPrivacyPolicyTypeVO();
				data.setLawItemList(policyTypeAPIVOList);
				data.setLawName(tPrivacyPolicyType.getLawName());
				data.setType(tPrivacyPolicyType.getType());

				list.add(data);
			}
			map.put("lawDepthData", list);
            return;
    	}else {
    		//查询用户法规的权限
        	List<String> lawUserRole = taskExtendMapper.findPushlawRole(task.getCreateUserId());
        	LOG.info("用户法规权限={}", lawUserRole==null ? "null" : lawUserRole.toString());
        	if (task.getTerminalType() == TerminalTypeEnum.IOS) {
                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_164)) {
                    // 164号文
                    map.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.IOS_LAW_164.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.IOS_LAW_164.id));
                }

                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_191)) {
                    //35273号文
                    map.put("miitLawInfo191", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.IOS_LAW_191.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap191", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.IOS_LAW_191.id));
                }

                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_35273)) {
                    //35273号文
                    map.put("miitLawInfo35273", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.IOS_LAW_35273.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap35273", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.IOS_LAW_35273.id));
                }
                
                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_41391)) {
                    //41391号文
                    map.put("miitLawInfo41391", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.IOS_LAW_41391.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap41391", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.IOS_LAW_41391.id));
                }
        	}else if (task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) { //微信小程序
        		if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_164)) {
                    // 164号文
                    map.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.WECHAT_APPLET_LAW_164.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.WECHAT_APPLET_LAW_164.id));
                }
                if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_191)) {
                    //191号文
                    map.put("miitLawInfo191", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.WECHAT_APPLET_LAW_191.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap191", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.WECHAT_APPLET_LAW_191.id));
                }

                if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_35273)) {
                    //35273号文
                    map.put("miitLawInfo35273", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.WECHAT_APPLET_LAW_35273.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap35273", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.WECHAT_APPLET_LAW_35273.id));
                }
                
                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_41391)) {
                    //41391号文
                    map.put("miitLawInfo41391", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.WECHAT_APPLET_LAW_41391.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap41391", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.WECHAT_APPLET_LAW_41391.id));
                }
        	}else if (task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) { //支付宝小程序
        		if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_164)) {
                    // 164号文
                    map.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ALIPAY_APPLET_LAW_164.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ALIPAY_APPLET_LAW_164.id));
                }
                if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_191)) {
                    //191号文
                    map.put("miitLawInfo191", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ALIPAY_APPLET_LAW_191.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap191", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ALIPAY_APPLET_LAW_191.id));
                }

                if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_35273)) {
                    //35273号文
                    map.put("miitLawInfo35273", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ALIPAY_APPLET_LAW_35273.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap35273", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ALIPAY_APPLET_LAW_35273.id));
                }
                
                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_41391)) {
                    //41391号文
                    map.put("miitLawInfo41391", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ALIPAY_APPLET_LAW_41391.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap41391", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ALIPAY_APPLET_LAW_41391.id));
                }
            } else {
            	if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_164)) {
                    // 164号文
                    map.put("miitLawInfo", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ANDROID_LAW_164.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ANDROID_LAW_164.id));
                }
                if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_191)) {
                    //191号文
                    map.put("miitLawInfo191", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ANDROID_LAW_191.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap191", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ANDROID_LAW_191.id));
                }

                if(lawUserRole!= null  && lawUserRole.toString().contains(ConstantsUtils.LAW_35273)) {
                    //35273号文
                    map.put("miitLawInfo35273", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ANDROID_LAW_35273.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap35273", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ANDROID_LAW_35273.id));
                }
                
                if (lawUserRole != null && lawUserRole.toString().contains(ConstantsUtils.LAW_41391)) {
                    //41391号文
                    map.put("miitLawInfo41391", miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.ANDROID_LAW_41391.id.longValue(), task.getTaskId()));
                    map.put("miitLawDetectResultMap41391", miitDetectService.findAllItemByTaskId(task.getTaskId(), PrivacyLawId.ANDROID_LAW_41391.id));
                }
           }
    	}
    }

    private List<String> setImgBase64(List<String> imgs){

    	List<String> list = new ArrayList<>();
    	if(imgs == null || imgs.size()==0) {
    		return list;
    	}

    	for (String string : list) {
    		if (StringUtils.isNotBlank(string)) {
            	if(string.contains("group")) {
            		String filePath = commonProperties.getProperty("ijiami.framework.file.path")+"/"+UUID.randomUUID().toString()+".png";
            		String fileUrl = null;
            		if(StringUtils.isNotBlank(string) && string.contains("http")) {
            			fileUrl = string;
            		}else {
            			fileUrl = commonProperties.getProperty("detection.result.url.prefix")+string;
            		}
            		try {
    					FileUtils.copyURLToFile(new URL(fileUrl), new java.io.File(filePath));
    				} catch (Exception e) {
    					e.getMessage();
    				}
            		java.io.File image = new java.io.File(filePath);
            		if(image.exists()) {
            			String base64 = reportChartService.getImageBASE64(filePath);
            			list.add(base64);
            			image.delete();
            		}
            	}else {
            		cn.ijiami.base.common.file.entity.File img = fileService.findFileByFileKey(string);
                    String base64 = reportChartService.getImageBASE64(commonProperties.getFilePath() + img.getFilePath());
                    list.add(base64);
            	}
            }
		}
    	return list;
    }

    private FileVO uploadFile(Long taskId, String filePath) {
        FileVO fileVO = new FileVO();
        FileInputStream inputStream = null;
        File file = new File(filePath);
        try {
            // 文件扩展名
            inputStream = new FileInputStream(file);

            fileVO.setFileExtName(FilenameUtils.getExtension(file.getName()));
            fileVO.setInputStream(inputStream);
            fileVO.setFileSize(file.length());
            fileVO.setFileName(file.getName());
            fileVO.setFilePath(filePath);
            fileVO = singleFastDfsFileService.instance().upload(fileVO);
        } catch (Exception e) {
            e.getMessage();
        }finally {
			if(inputStream!= null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.getMessage();
				}
				if (StringUtils.isBlank(fileVO.getFileUrl())) {
	                file.delete();
	            } else {
                    storageLogService.saveStorageLogByTaskFile(taskId, fileVO.getFileUrl());
                }
			}
		}
        return fileVO;
    }

    //阶段行为数据
    private Map<String, Object> behaviorInfo(TTask task, BehaviorStageEnum behaviorStage) {

        Map<String, Object> map = new HashMap<>();

        //应用行为
        List<TPrivacyActionNougat> behaviorAction = new ArrayList<>();
        try {
            List<TPrivacyActionNougat> behaviorActionList = privacyActionNougatService
                    .findByTaskIdAndExecutors(task.getTaskId(), "", behaviorStage.getValue());
            if (behaviorActionList != null && behaviorActionList.size() > 0) {
                behaviorActionList.forEach(action -> {
                    List<TPrivacyActionNougat> behaviorActionDetailList = privacyActionNougatService
                            .findByTaskIdAndActionIdAndExecutors(task.getTaskId(), action.getActionId(), "", behaviorStage.getValue());
                    if (behaviorActionDetailList != null && behaviorActionDetailList.size() > 0) {
                        behaviorActionDetailList.forEach(detail -> {
                            detail.setActionName(action.getActionName());
                            detail.setActionPermission(action.getActionPermission());
                            detail.setActionPermissionAlias(action.getActionPermissionAlias());
                            detail.setCounter(action.getCounter());
                            detail.setSensitive(action.getSensitive());
                            detail.setBehaviorStage(behaviorStage);
                            if(StringUtils.isBlank(detail.getJniStackInfo())) {
                            	detail.setJniStackInfo("-");
                            }
                            behaviorAction.add(detail);
                        });
                    }

                });
            }
        } catch (Exception e) {
            e.getMessage();
        }
        
        try {
			if(behaviorAction != null && behaviorAction.size()>0){
				behaviorAction
			    .stream()
			    .peek(ActionNougatUtils::setTriggerCycle)
			    .collect(Collectors.toList());
			}
		} catch (Exception e) {
			e.getMessage();
		}
        
        map.put("applicationBehaviors", behaviorAction); //应用行为

        List<PermissionVO> permissionBehaviors = analyzePermissionBehaviors(task, behaviorStage.getValue());
        map.put("analyzePermissionBehaviors", permissionBehaviors); //权限行为分析

        List<TPrivacyOutsideAddress> outsideList = new ArrayList<>();
        //境内0
        List<TPrivacyOutsideAddress> outsideAddress_0_list = privacyOutsideAddressService.findByTaskIdAndOutside(task.getTaskId(), 0, behaviorStage.getValue());
        if (outsideAddress_0_list != null && outsideAddress_0_list.size() > 0) {
            outsideAddress_0_list.forEach(outside -> {
                List<TPrivacyOutsideAddress> outsideAddressStackInfoLiist = privacyOutsideAddressService.
                        findByTaskIdAndOutsideStackInfo(task.getTaskId(), outside.getIp(), outside.getHost(), behaviorStage.getValue());
                if (outsideAddressStackInfoLiist != null && outsideAddressStackInfoLiist.size() > 0) {
                    outsideAddressStackInfoLiist.forEach(stack -> {
                        stack.setAppCount(outside.getAppCount());
                        stack.setSdkCount(outside.getSdkCount());
                        stack.setCounter(outside.getCounter());
                        stack.setBehaviorStage(behaviorStage);
                        outsideList.add(stack);
                    });
                }
            });
        }
        //境外1
        List<TPrivacyOutsideAddress> outsideAddress_1_list = privacyOutsideAddressService.findByTaskIdAndOutside(task.getTaskId(), 1, behaviorStage.getValue());
        if (outsideAddress_1_list != null && outsideAddress_1_list.size() > 0) {
            outsideAddress_1_list.forEach(outside -> {
                List<TPrivacyOutsideAddress> outsideAddressStackInfoLiist = privacyOutsideAddressService.
                        findByTaskIdAndOutsideStackInfo(task.getTaskId(), outside.getIp(), outside.getHost(), behaviorStage.getValue());
                if (outsideAddressStackInfoLiist != null && outsideAddressStackInfoLiist.size() > 0) {
                    outsideAddressStackInfoLiist.forEach(stack -> {
                        stack.setAppCount(outside.getAppCount());
                        stack.setSdkCount(outside.getSdkCount());
                        stack.setCounter(outside.getCounter());
                        stack.setBehaviorStage(behaviorStage);
                        outsideList.add(stack);
                    });
                }
            });
        }
        map.put("commTransmitBehaviors", outsideList); //通讯传输行为分析

        List<CountSensitiveTypeVO> typeVOList = privacySensitiveWordService
                .countSensitiveTypeByTaskId(task.getTaskId(), behaviorStage.getValue());
        List<TPrivacySensitiveWord> sensitiveWork = new ArrayList<>();
        if (typeVOList != null && typeVOList.size() > 0) {
            for (CountSensitiveTypeVO countSensitiveTypeVO : typeVOList) {
                List<CountSensitiveNameVO> nameVOList = privacySensitiveWordService
                        .countSensitiveNameByTaskId(task.getTaskId(), countSensitiveTypeVO.getTypeId(), behaviorStage.getValue());
                if (nameVOList == null || nameVOList.size() == 0) {
                    continue;
                }
                for (CountSensitiveNameVO countSensitiveNameVO : nameVOList) {
                    List<TPrivacySensitiveWord> sensitiveWorkList = privacySensitiveWordService.
                            findByTaskIdAndTypeIdAndName(task.getTaskId(), countSensitiveNameVO.getTypeId(), countSensitiveNameVO.getName(), behaviorStage.getValue());
                    if (sensitiveWorkList == null || sensitiveWorkList.size() == 0) {
                        continue;
                    }

                    for (TPrivacySensitiveWord word : sensitiveWorkList) {
                        word.setTypeName(countSensitiveTypeVO.getTypeName());
                        sensitiveWork.add(word);
                    }
                }
            }
        }
        map.put("transmitPersonalDetails", sensitiveWork);//传输个人信息

        List<CountSharedPrefsTypeVO> prefsTypeVOList = privacySharedPrefsService
                .countSharedPrefsTypeByTaskId(task.getTaskId(), behaviorStage.getValue());
        List<TPrivacySharedPrefs> sharePrefs = new ArrayList<>();
        if (prefsTypeVOList != null && prefsTypeVOList.size() > 0) {
            for (CountSharedPrefsTypeVO countSharedPrefsTypeVO : prefsTypeVOList) {
                List<CountSharedPrefsNameVO> prefsNameList = privacySharedPrefsService
                        .countSharedPrefsNameByTaskId(task.getTaskId(), countSharedPrefsTypeVO.getTypeId(), behaviorStage.getValue());
                if (prefsNameList == null || prefsNameList.size() == 0) {
                    continue;
                }
                for (CountSharedPrefsNameVO countSharedPrefsNameVO : prefsNameList) {
                    List<TPrivacySharedPrefs> sharePrefsList = privacySharedPrefsService
                            .findByTaskIdAndTypeIdAndName(task.getTaskId(), countSharedPrefsNameVO.getTypeId(), countSharedPrefsNameVO.getName(), behaviorStage.getValue());
                    if (sharePrefsList == null || sharePrefsList.size() == 0) {
                        continue;
                    }
                    for (TPrivacySharedPrefs tPrivacySharedPrefs : sharePrefsList) {
                        tPrivacySharedPrefs.setTypeName(countSharedPrefsTypeVO.getTypeName());
                        sharePrefs.add(tPrivacySharedPrefs);
                    }
                }
            }
        }
        map.put("storePersonalDetails", sharePrefs); //存储个人信息

        return map;
    }

    private List<PermissionVO> analyzePermissionBehaviors(TTask task, Integer behaviorStage) {
        List<PermissionVO> list = new ArrayList<>();

        // 申明权限
        List<PermissionVO> vo1 = this.getXMLPermission(task, behaviorStage);
        if (vo1 != null && vo1.size() > 0) {
            vo1.forEach(vo -> {
                vo.setPermissionType(1);
            });
        }
        // 敏感权限
        List<PermissionVO> vo2 = this.getSensitivePermission(task, behaviorStage);
        if (vo2 != null && vo2.size() > 0) {
            vo2.forEach(vo -> {
                vo.setPermissionType(2);
            });
        }
        // 尝试使用未申明权限
        List<PermissionVO> vo3 = this.getNoDeclaredPermission(task, behaviorStage);
        if (vo3 != null && vo3.size() > 0) {
            vo3.forEach(vo -> {
                vo.setPermissionType(3);
            });
        }
        // 越权权限
        List<PermissionVO> vo4 = this.getExcessPermission(task, behaviorStage);
        if (vo4 != null && vo4.size() > 0) {
            vo4.forEach(vo -> {
                vo.setPermissionType(4);
            });
        }
        list.addAll(vo1);
        list.addAll(vo2);
        list.addAll(vo3);
        list.addAll(vo4);
        return list;
    }

    @Override
    public JSONObject getDetectionForTencent(TTask task) {

//		TTaskExtend extend = taskExtendMapper.findTaskByBusinessId(bussinessId);
//		if(extend == null) {
//			LOG.error("extend is null bussinessId={}",bussinessId);
//			return null;
//		}
//		
//		TTask task = taskService.findById(extend.getTaskId());
//		if(task==null) {
//			LOG.error("task is null taskId={}",extend.getTaskId());
//			return null;
//		}

        JSONObject result = new JSONObject();

        JSONObject summary2 = new JSONObject();
        summary2.put("base_info", baseInfo(task));
        summary2.put("privacy_risk_item", privacy_risk_item(task));
        summary2.put("permission_information_item", permission_information_item(task));
        summary2.put("application_behavior_item", application_behavior_item(task));
        summary2.put("laws_transmission_item", laws_transmission_item(task));
        summary2.put("laws_storage_item", laws_storage_item(task));
        summary2.put("overseas_communication_item", overseas_communication_item(task));
        summary2.put("sdk_item", sdk_item(task));

        result.put("summary1", risk_count(summary2.getJSONObject("privacy_risk_item"))); //摘要1
        result.put("summary2", summary2);  //摘要2

        return result;
    }

    private JSONObject risk_count(JSONObject privacy_risk_item) {
        JSONObject summary1 = new JSONObject();
        summary1.put("risk_count", 0);

        int count = 0;
        if (privacy_risk_item != null) {
            count = count + privacy_risk_item.getIntValue("illegal_collection_risk_count"); //违规收集个人信息数量          id=6
            count = count + privacy_risk_item.getIntValue("outside_collection_risk_count"); //超范围收集个人信息数量    id=7
            count = count + privacy_risk_item.getIntValue("illegal_use_risk_count");        //违规使用个人信息数量  id=8
            count = count + privacy_risk_item.getIntValue("use_push_risk_count");           //强制用户使用定向推送功能数量    id=9
            count = count + privacy_risk_item.getIntValue("excessive_access_risk_count");   //APP强制、频繁、过度索取权限数量  10
            count = count + privacy_risk_item.getIntValue("association_start_risk_count");  //APP频繁自启动和关联启动  11

            summary1.put("risk_count", count);
        }
        return summary1;
    }

    /**
     * 基本信息
     *
     * @param task
     * @return
     */
    private JSONObject baseInfo(TTask task) {
        BaseMessageVO baseMessageVO = taskService.getBaseMessage(task.getApkDetectionDetailId());
        JSONObject baseInfo = new JSONObject();
        if (baseMessageVO == null) {
            return baseInfo;
        }
        baseInfo.put("apk_logo", baseMessageVO.getApkLogo());
        baseInfo.put("app_name", baseMessageVO.getAppName());
        baseInfo.put("app_version", baseMessageVO.getVersionName());
        baseInfo.put("app_package", baseMessageVO.getPackageName());
        baseInfo.put("app_size", baseMessageVO.getApkSize());
        baseInfo.put("target_sdk_version", baseMessageVO.getTargetSdkVersion());
        baseInfo.put("min_sdk_version", baseMessageVO.getMinSdkVersion());
        baseInfo.put("signature", baseMessageVO.getSignDetail());
        baseInfo.put("sign_md5", baseMessageVO.getSignMd5());
        baseInfo.put("file_md5", baseMessageVO.getApkMd5());
        baseInfo.put("apk_is_reinforce", baseMessageVO.getEncryptDetail() == null ? "未加固" : baseMessageVO.getEncryptDetail());
        if (task.getTaskStarttime() != null && task.getTaskEndtime() != null) {
            baseInfo.put("detection_time", DateUtils.getDistanceTime(task.getTaskEndtime(), task.getTaskStarttime()));
        }
        if (task.getTaskStarttime() != null) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            baseInfo.put("start_time", format.format(task.getTaskStarttime()));
        }
        return baseInfo;
    }

    /**
     * 风险项的详细统计
     *
     * @return
     */
    private JSONObject privacy_risk_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("illegal_collection_risk_count", 0); //违规收集个人信息数量          id=6
        data.put("outside_collection_risk_count", 0); //超范围收集个人信息数量    id=7
        data.put("illegal_use_risk_count", 0);        //违规使用个人信息数量  id=8
        data.put("use_push_risk_count", 0);           //强制用户使用定向推送功能数量    id=9
        data.put("excessive_access_risk_count", 0);   //APP强制、频繁、过度索取权限数量  10
        data.put("association_start_risk_count", 0);  //APP频繁自启动和关联启动  11


        CountLawDetectResultDTO result = miitDetectService.findLawDetectResultByTaskId(PrivacyLawId.law164(task.getTerminalType()).id.longValue(), task.getTaskId());
        if (result == null) {
            return data;
        }

        LawDetectResultDTO lawResult = result.getLawDetectResult();
        if (lawResult == null || lawResult.getNextLaws() == null || lawResult.getNextLaws().size() == 0) {
            return data;
        }

        List<LawDetectResultDTO> nextLaws = lawResult.getNextLaws(); //大项
        for (LawDetectResultDTO LawDetectResultDTO : nextLaws) {      //APP、SDK违规处理用户个人信息方面"
            List<LawDetectResultDTO> nextLaws_item = LawDetectResultDTO.getNextLaws(); //小项

            if (nextLaws_item != null && nextLaws_item.size() > 0) {
                setRiskItemPath(data, nextLaws_item);
            }
        }

        return data;
    }


    private JSONObject setRiskItemPath(JSONObject data, List<LawDetectResultDTO> nextLaws_item) {
        for (LawDetectResultDTO LawDetectResultDTO : nextLaws_item) {
            int count = 0;
            List<LawDetectResultDTO> lawDetectList = LawDetectResultDTO.getNextLaws();
            for (LawDetectResultDTO LawDetectResultDTO2 : lawDetectList) {
                if (LawDetectResultDTO2.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE) {
                    count++;
                }
            }
            if (ILLEGAL_COLLECTION_RISK_IDS.contains(LawDetectResultDTO.getId())) {
                data.put("illegal_collection_risk_count", count);
            }
            if (OUTSIDE_COLLECTION_RISK_IDS.contains(LawDetectResultDTO.getId())) {
                data.put("outside_collection_risk_count", count);
            }
            if (ILLEGAL_USE_RISK_IDS.contains(LawDetectResultDTO.getId())) {
                data.put("illegal_use_risk_count", count);
            }
            if (USE_PUSH_RISK_IDS.contains(LawDetectResultDTO.getId())) {
                data.put("use_push_risk_count", count);
            }
            if (EXCESSIVE_ACCESS_RISK_IDS.contains(LawDetectResultDTO.getId())) {
                data.put("excessive_access_risk_count", count);
            }
            if (ASSOCIATION_START_RISK_IDS.contains(LawDetectResultDTO.getId())) {
                data.put("association_start_risk_count", count);
            }
        }
        return data;
    }

    /**
     * 权限信息统计项
     *
     * @return
     */
    private JSONObject permission_information_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("use_statement_permission_count", 0); //声明权限
        data.put("no_statement_permission_count", 0);  //尝试使用未声明权限

        // 申明权限
        List<PermissionVO> vo1List = this.getXMLPermission(task.getApkDetectionDetailId(), null);
        if (vo1List != null && vo1List.size() > 0) {
            data.put("use_statement_permission_count", vo1List.size());
        }
        // 敏感权限
//        List<PermissionVO> vo2 = this.getSensitivePermission(documentId, behaviorStage);
        // 尝试使用未申明权限
        List<PermissionVO> vo3List = this.getNoDeclaredPermission(task.getApkDetectionDetailId(), null);
        if (vo3List != null && vo3List.size() > 0) {
            data.put("no_statement_permission_count", vo3List.size());
        }
        // 越权权限
//        List<PermissionVO> vo4 = this.getExcessPermission(documentId, behaviorStage);

        return data;
    }

    /**
     * 应用行为统计项
     *
     * @return
     */
    private JSONObject application_behavior_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("behavior_type_count", 0); //应用行为类型数(多少种)
        data.put("behavior_count", 0);      //应用行为总数

        List<TPrivacyActionNougat> list = privacyActionNougatService.countBehaviorsByTaskId(task.getTaskId(), true);
        if (list != null && list.size() > 0) {
            data.put("behavior_type_count", list.size());
            int behavior_count = 0;
            for (TPrivacyActionNougat tPrivacyActionNougat : list) {
                behavior_count = behavior_count + tPrivacyActionNougat.getCounter();
            }
            data.put("behavior_count", behavior_count);
        }
        return data;
    }

    /**
     * 明文传输个人信息统计项
     *
     * @param task
     * @return
     */
    private JSONObject laws_transmission_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("laws_transmission_type_count", 0);//明文传输类型数
        data.put("laws_transmission_count", 0);     //明文传输总数

        List<CountSensitiveTypeVO> list = privacySensitiveWordService.countSensitiveTypeByTaskId(task.getTaskId(), null);
        if (list != null && list.size() > 0) {
            data.put("laws_transmission_type_count", list.size());
            int laws_transmission_count = 0;
            for (CountSensitiveTypeVO countSensitiveTypeVO : list) {
                laws_transmission_count = laws_transmission_count + countSensitiveTypeVO.getTypeCount();
            }
            data.put("laws_transmission_count", laws_transmission_count);
        }

        return data;
    }

    /**
     * 明文存储个人信息统计项
     *
     * @param task
     * @return
     */
    private JSONObject laws_storage_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("laws_storage_type_count", 0);//明文存储触发类型数
        data.put("laws_storage_count", 0);     //明文存储触发总数

        List<CountSharedPrefsTypeVO> list = privacySharedPrefsService.countSharedPrefsTypeByTaskId(task.getTaskId(), null);

        if (list != null && list.size() > 0) {
            data.put("laws_storage_type_count", list.size());
            int laws_storage_type_count = 0;
            for (CountSharedPrefsTypeVO countSharedPrefsTypeVO : list) {
                laws_storage_type_count = laws_storage_type_count + countSharedPrefsTypeVO.getTypeCount();
            }
            data.put("laws_storage_type_count", laws_storage_type_count);
        }
        return data;
    }

    /**
     * 境内外通信访问统计项
     *
     * @return
     */
    private JSONObject overseas_communication_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("overseas_ip_count", 0);  //境外IP/域名共触发数
        data.put("overseas_count", 0);     //境外触发总数
        data.put("native_ip_count", 0);    //境内IP/域名共触发数
        data.put("native_count", 0);       //境内触发总数


        List<CountOutsideTypeVO> list = privacyOutsideAddressService.countByOutside(task.getTaskId(), null);
        if (list != null && list.size() > 0) {
            CountOutsideTypeVO vo = list.get(0);
            data.put("overseas_ip_count", vo.getOutside());  //境外IP/域名共触发数
            data.put("native_ip_count", vo.getCounter());    //境内IP/域名共触发数

        }

        int overseas_count = 0;
        int native_count = 0;
        //境内
        List<TPrivacyOutsideAddress> nativeList = privacyOutsideAddressService.findByTaskIdAndOutside(task.getTaskId(), 0, null);
        if (nativeList != null && nativeList.size() > 0) {
            for (TPrivacyOutsideAddress tPrivacyOutsideAddress : nativeList) {
                native_count = native_count + tPrivacyOutsideAddress.getCounter();
            }
        }
        //境外
        List<TPrivacyOutsideAddress> outsideList = privacyOutsideAddressService.findByTaskIdAndOutside(task.getTaskId(), 1, null);
        if (outsideList != null && outsideList.size() > 0) {
            for (TPrivacyOutsideAddress tPrivacyOutsideAddress : outsideList) {
                overseas_count = overseas_count + tPrivacyOutsideAddress.getCounter();
            }
        }
        data.put("overseas_count", overseas_count);     //境外触发总数
        data.put("native_count", native_count);       //境内触发总数

        return data;
    }

    /**
     * SDK统计项
     *
     * @param task
     * @return
     */
    private JSONObject sdk_item(TTask task) {
        JSONObject data = new JSONObject();
        data.put("sdk_count", 0);
        //SDK信息
        try {
            List<SdkVO> sdkInfo = getSDKList(task.getApkDetectionDetailId(), task.getTaskId());
            if (sdkInfo != null && sdkInfo.size() > 0) {
                data.put("sdk_count", sdkInfo.size());
            }
        } catch (IjiamiApplicationException e) {
            e.getMessage();
        }
        return data;
    }

    @Override
    public JSONObject getDetectionForTencentDetail(TTask task) {

        JSONObject jsonData = new JSONObject();

        //检测基本信息
        BaseMessageVO baseMessageVO = taskService.getBaseMessage(task.getApkDetectionDetailId());
        JSONObject baseInfoJson = new JSONObject();
        baseInfoJson.put("apkLogo", baseMessageVO.getApkLogo());
        baseInfoJson.put("appName", baseMessageVO.getAppName());
        baseInfoJson.put("appVersion", baseMessageVO.getVersionName());
        baseInfoJson.put("appPackage", baseMessageVO.getPackageName());
        baseInfoJson.put("appSize", baseMessageVO.getApkSize());
        baseInfoJson.put("targetSdkVersion", baseMessageVO.getTargetSdkVersion());
        baseInfoJson.put("signature", baseMessageVO.getSignDetail());
        baseInfoJson.put("fileMD5", baseMessageVO.getApkMd5());
        baseInfoJson.put("minSdkVersion", baseMessageVO.getMinSdkVersion());
        baseInfoJson.put("signMD5", baseMessageVO.getSignMd5());
        baseInfoJson.put("detectionTime", baseMessageVO.getApkDetectionStarttime());
        baseInfoJson.put("detectionTimeConsuming", baseMessageVO.getApkDetectionTime());
        baseInfoJson.put("apkIsReinforce", baseMessageVO.getEncryptDetail() == null ? "未加固" : baseMessageVO.getEncryptDetail());
        
        baseInfoJson.put("accountSubject",baseMessageVO.getAccountSubject()); //小程序账号主体
        baseInfoJson.put("appId",baseMessageVO.getAppId()); //小程序APPid
        baseInfoJson.put("originalId",baseMessageVO.getOriginalId()); //小程序原始账号ID
        baseInfoJson.put("plugins",baseMessageVO.getPlugins()); //小程序引用插件
        baseInfoJson.put("privacyData",baseMessageVO.getPrivacyData());  //小程序服务隐私及数据提示
        baseInfoJson.put("serviceCategory",baseMessageVO.getServiceCategory()); //小程序服务类目
        baseInfoJson.put("serviceProvider",baseMessageVO.getServiceProvider()); //小程序授权服务商
        baseInfoJson.put("statement",baseMessageVO.getStatement()); //小程序服务声明
        baseInfoJson.put("updateTime",baseMessageVO.getUpdateTime()); //小程序更新时间
        
        
        jsonData.put("baseInfo", baseInfoJson);

        jsonData.put("permissionInfo", new JSONArray()); //权限信息
        jsonData.put("personalRisks", new JSONArray()); //漏洞信息
        jsonData.put("sdkInfo", new JSONArray());  //sdk信息
        jsonData.put("complianceRisks", new JSONArray());//合规性信息

        List<PermissionVO> perimssList = new ArrayList<>();
        // 申明权限
        List<PermissionVO> vo1List = this.getXMLPermission(task, null);
        // 敏感权限
        List<PermissionVO> vo2List = this.getSensitivePermission(task, null);
        // 尝试使用未申明权限
        List<PermissionVO> vo3List = this.getNoDeclaredPermission(task, null);
        // 越权权限
        List<PermissionVO> vo4List = this.getExcessPermission(task, null);
		
		 // 申明权限
        if(vo1List != null && vo1List.size()>0) {
        	vo1List.forEach(vo -> {
                vo.setPermissionType(1);
            });
        	perimssList.addAll(vo1List);
        }
        
        // 敏感权限
        if(vo2List!= null && vo2List.size()>0) {
        	vo2List.forEach(vo -> {
                vo.setPermissionType(2);
            });
        	perimssList.addAll(vo2List);
        }
        
        // 尝试使用未申明权限
        if(vo3List!= null && vo3List.size()>0) {
        	vo3List.forEach(vo -> {
                vo.setPermissionType(3);
            });
        	perimssList.addAll(vo3List);
        }
        
        // 越权权限
        if(vo4List!= null && vo4List.size()>0) {
        	vo4List.forEach(vo -> {
                vo.setPermissionType(4);
            });
        	perimssList.addAll(vo4List);
        }

        if (perimssList != null && perimssList.size() > 0) {
            jsonData.put("permissionInfo", perimssList);
        }

        //个人信息漏洞信息
        PersonalInfoRiskDetailVO personalRisks = iPrivacyDetectionTransferRiskService.getPersonalInfoRiskDetail(task.getApkDetectionDetailId());
        if (personalRisks != null && personalRisks.getList() != null) {
            List<PrivacyDetectionResultVO> list = personalRisks.getList();
            if (CollectionUtils.isNotEmpty(list)) {
            	jsonData.put("personalRisks", list);
            }
        }

        //sdk信息
        try {
            List<SdkVO> sdkList = getSDKList(task.getApkDetectionDetailId(), task.getTaskId());
            if (sdkList != null && sdkList.size() > 0) {
                jsonData.put("sdkInfo", sdkList);
            }
        } catch (Exception e) {
            e.getMessage();
        }

        //合规性信息
        List<TPrivacyPolicyResult> lawRiskList = iPrivacyDetectionTransferRiskService.getLawInfoRiskDetail(task.getApkDetectionDetailId());
        if (lawRiskList != null && lawRiskList.size() > 0) {
            jsonData.put("complianceRisks", lawRiskList);
        }

        //应用行为数据文件地址（fastDFS地址）appBehaviorMap存储
        jsonData.put("behaviorDataFile", "");
        String fast_url = getBehaviorDataFile(task);
        if (StringUtils.isNoneBlank(fast_url) && fast_url.contains("group")) {
            String prex = commonProperties.getProperty("detection.result.url.prefix");
            jsonData.put("behaviorDataFile", prex + fast_url);
        }
		return jsonData;
	}

	/**
	 * 查询标记的记录
	 * @param bId
	 * @param resultType
	 */
	@Override
	public List<TPrivacyResultMark> getMarkResultRecord(Long bId, LawResultTypeEnum resultType){
		List<TPrivacyResultMark> list = privacyResultMarkMapper.findByResultType(bId, resultType);
		if(list != null && list.size()>0) {
			for (TPrivacyResultMark tPrivacyResultMark : list) {
				LawResultMarkStatusEnum em = tPrivacyResultMark.getResultStatus();
				if(resultType==LawResultTypeEnum.LAW_164
                        || resultType==LawResultTypeEnum.LAW_191
                        || resultType==LawResultTypeEnum.LAW_35273
                        || resultType==LawResultTypeEnum.LAW_41391) {
					em.setName(em.getValue()==1?"存在风险":"未发现风险");
				}else {
					//1正确    2误报
					em.setName(em.getValue()==1?"正确":"误报");
				}
				tPrivacyResultMark.setResultStatus(em);
			}
		}
		return list;
    }

    @Override
    public BehaviorPermissionVo getBehaviorApplyNameAndPermission(String documentId, Integer behaviorStage ,String packageName) {
        TTask task = taskService.findByDocumentId(documentId);
        return privacyActionNougatService.getBehaviorApplyNameAndPermission(task.getTaskId(), behaviorStage,packageName);
    }

    @Override
    public BehaviorPermissionVo getLawDetailBehaviorApplyNameAndPermission(String documentId, Integer dataType, String itemNo) {
        TTask task = taskService.findByDocumentId(documentId);
        return privacyActionNougatService.getLawDetailBehaviorApplyNameAndPermission(task.getTaskId(), dataType, itemNo);
    }

    @Override
    public IosRemoteToolConfig getIosAutoDetectionRemoteConfig(Long taskId) {
	    LOG.info("获取IOS远程工具配置 taskId={}", taskId);
        IosRemoteToolConfig remoteToolConfig = new IosRemoteToolConfig();
        remoteToolConfig.setUrl(commonProperties.getProperty("ijiami.ios.remote.tool.url"));
        remoteToolConfig.setUploadUrl(commonProperties.getProperty("tracker_server"));
        return remoteToolConfig;
    }

    @Override
    public PrivacyDetectionUploadFileVO uploadFile(MultipartFile file, Long taskId) throws IOException, IjiamiApplicationException {
        LOG.info("上传文件 file={} taskId={}", file.getName(), taskId);
        String filePath = commonProperties.getFilePath() + File.separator + "default" + File.separator + file.getOriginalFilename();
        File temp = new File(filePath);
        try {
            file.transferTo(temp);
            FileVO upoladFile = uploadFile(taskId, filePath);
            PrivacyDetectionUploadFileVO result = new PrivacyDetectionUploadFileVO();
            result.setUrl(fastDFSIp + "/" + upoladFile.getFileUrl());
            return result;
        } finally {
            temp.delete();
        }
    }

    @Override
    public TReportStore saveReportStore(File file, Long userId, Long taskId, String paramMd5) {
        try {
            FileVO fileVo = uploadFile(taskId, file.getAbsolutePath());

            if(StringUtils.isBlank(fileVo.getFileUrl())) {
                return null;
            }
            TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(taskId);
            TReportStore store = new TReportStore();
            store.setCreateTime(new Date());
            store.setCreateUserId(userId);
            store.setName(file.getName());
            store.setPath(fileVo.getFileUrl());
            store.setBussinessId(extend.getBussinessId());
            store.setTaskId(taskId);
            store.setParamMd5(paramMd5);

            if(file.getAbsolutePath().toLowerCase().contains("doc")){
                store.setType(1);
            }
            if(file.getAbsolutePath().toLowerCase().contains("pdf")){
                store.setType(2);
            }
            if(file.getAbsolutePath().toLowerCase().contains("zip")){
                store.setType(3);
            }
            if(file.getAbsolutePath().toLowerCase().contains("xlsx")){
                store.setType(4);
            }
            reportStoreMapper.insertSelective(store);
            return store;
        } catch (Exception e) {
            e.getMessage();
        }
        return null;
    }

    /**
     * 判断资产表中的资产类型跟数据类型表中的类型都不存在则返回true，否则为false
     * @param privacyCategories
     * @param assets
     * @return
     */
    private boolean isTrueOrFalse(List<TPrivacyCategory> privacyCategories,TAssets assets){
	    if(CollectionUtils.isEmpty(privacyCategories) && assets==null){
	        return true;
        }
        if(StringUtils.isEmpty(assets.getAssetsFunctionType())){
	        return true;
        }
	    return false;
    }


    /**
     * API单个下载报告任务
     * @param taskId 任务id
     * @param type   1word 2pdf(html转换) 3html
     * @return
     * @throws IjiamiApplicationException
     */
    @Override
    public File downloadAutoReport(Long taskId, Integer type) throws IjiamiApplicationException {
        ReportResultVO vo = downloadAutoDetectReportApi(taskId, type, null);
        return vo == null ? null : vo.report();
    }

    @Override
    public ReportResultVO downloadAutoDetectReportApi(Long taskId, Integer type, String[] itemNo) throws IjiamiApplicationException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        TaskDetailVO taskDetailVO = detectionMongodbService.findDetectionResultExcludeField(task.getApkDetectionDetailId(), fields);
        // 组装报告名称
        String outFileName = filenameFilter(CommonUtil.reportDocName(taskDetailVO, "全自动")); //过滤非法关键词名称
        // 组装返回对象
        ReportResultVO reportResultVO = new ReportResultVO();
        reportResultVO.appName(taskDetailVO.getApk_name());
        reportResultVO.versionCode(taskDetailVO.getApk_version());
        reportResultVO.terminalType(task.getTerminalType());
        // 查询模板
        TReportDesign reportDesign = reportDesignMapper.findByUserIdAndReportObject(task.getCreateUserId(), 3, task.getTerminalType().getValue());
        if (reportDesign == null) {
            reportDesign = reportDesignMapper.findByUserIdAndReportObject(null, 3, task.getTerminalType().getValue());
        }
        Map<String, Object> reportData = new HashMap<>(32);
        reportData.put("reportDesign", reportDesign);
        // android、ios报告 word -> pdf
        //        if (task.getTerminalType() == TerminalTypeEnum.IOS) {
        // 1.word 2.pdf
        if (type == 1 || type == 2) {
            String templateName = reportDesign.getTemplatePath().substring(reportDesign.getTemplatePath().lastIndexOf("/") + 1);
            try{
                cn.ijiami.base.common.file.entity.File logoFile = fileService.findFileByFileKey(reportDesign.getReportLogo());
                reportDesign.setReportLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + logoFile.getFilePath()));
                cn.ijiami.base.common.file.entity.File companyLogoFile = fileService.findFileByFileKey(reportDesign.getCompanyLogo());
                reportDesign.setCompanyLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + companyLogoFile.getFilePath()));
                cn.ijiami.base.common.file.entity.File headerLogoFile = fileService.findFileByFileKey(reportDesign.getHeaderLogo());
                reportDesign.setHeaderLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + headerLogoFile.getFilePath()));
                cn.ijiami.base.common.file.entity.File watermarkLogoFile = fileService.findFileByFileKey(reportDesign.getWatermarkLogo());
                reportDesign.setWatermarkLogo(reportChartService.getImageBASE64(commonProperties.getFilePath() + watermarkLogoFile.getFilePath()));
            }catch (Exception e){
                e.getMessage();
            }
            // 存放检测获取到的数据
            reportData.putAll(buildReportData(task.getApkDetectionDetailId(), type));

            Map<String, Object> map = toReportWordData(reportData);
            // 生成word、pdf
            String fileName = generateReport(map, templateName, outFileName, type);
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            File file = new File(reportRootPath + "out" + File.separator + fileName);
            if (file.exists()) {
                // word需要更新页码下标
                if (type == ReportTypeEnum.WORD.getValue()) {
                    String pageUrl = commonProperties.getProperty("ijiami.report.page.url");
                    if (StringUtils.isNoneBlank(pageUrl)) {
                        WordPageUtils.urlPost(pageUrl, file.getAbsolutePath(), file.getAbsolutePath());
                    }
                }
                //保存报告记录
                String prex = commonProperties.getProperty("detection.result.url.prefix");
                TReportStore store = saveReportStore(file, task.getCreateUserId(), taskId, null);
                reportResultVO.reportName(store.getName());
                reportResultVO.url(prex+store.getPath());
                reportResultVO.report(file);
                return reportResultVO;
            }
        }

        return null;
    }

	@Override
	public Map<String, Object> downloadAutoReportApi(String bussinessId, Integer[] type, String[] itemNo, User user) throws IjiamiApplicationException {
		TTask apiTask = taskExtendMapper.findTaskByBussinessId(bussinessId);
        if(type==null) {
            type = new Integer[]{1};
        }
        if(apiTask==null){
            throw new IjiamiApplicationException("任务不存在！");
        }

//        if(apiTask.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED || apiTask.getTaskTatus()!=DetectionStatusEnum.DETECTION_OVER) {
//            throw new IjiamiApplicationException("任务没有检测完成！");
//        }
        
        Map<String,Object> map = new HashMap<String,Object>();

        
        String param = bussinessId+StringUtils.join(type, ",")+(itemNo==null?"":StringUtils.join(itemNo, ","));
        String prex = commonProperties.getProperty("detection.result.url.prefix");
        String paramMd5 = Md5Utils.md5Hex(param);
        
        TReportStore store = reportStoreMapper.findByBussinessIdAndParamMd5(bussinessId, paramMd5);
        if(store != null) {
        	map.put("bussinessId", bussinessId);
            map.put("reportName", store.getName()==null?"":store.getName());
            map.put("url", prex+store.getPath());
            return map;
        }
        
        
        boolean isCheckAction = false;
        //是否选中行为报告
        if(itemNo != null && StringUtils.join(itemNo, ",").contains(ConstantsUtils.REPORT_NUM_4001)) {
        	isCheckAction = true;
        }
        
        String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
        reportDir.mkdirs();
        
        TaskReportDownLoadQuery taskReportQuery = new TaskReportDownLoadQuery();
        taskReportQuery.setItemNo(itemNo);
        File newFile = null;
        
        if (type.length == 1) {
	        //单个下载
	    	ReportResultVO file = getAutoDetectReportWithCache(apiTask.getTaskId(), type[0], taskReportQuery);
	        if (file == null || !file.report().exists()) {
	            throw new IjiamiApplicationException("下载报告异常！");
	        }
	        newFile = file.report();
            LOG.info("开始给报告增加隐形水印");
            addWatermarkService.watermarkToReport(newFile,user);
			if(isCheckAction && file.report().exists()) {
	        	try {
					FileUtils.copyFileToDirectory(file.report(), reportDir);
				} catch (IOException e) {
					e.getMessage();
				}
	        }
	        //是否选中行为报告
	        if(isCheckAction) {
	        	downloadExcel(apiTask.getTaskId(), reportDir,user);
	            String zipName = CommonUtil.reportZipName(file);
	            String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
	            newFile = new File(compress);
	        }
	        store = saveReportStore(newFile, apiTask.getCreateUserId(), apiTask.getTaskId(), paramMd5);
	        if(store==null) {
	            throw new IjiamiApplicationException("报告生成异常！");
	        }
	        
	        map.put("bussinessId", bussinessId);
	        map.put("reportName", store.getName()==null?"":store.getName());
	        map.put("url", prex+store.getPath());
	        return map;
        }
        
        //zip包
        //批量下载
        ReportResultVO reportResultVO = null;
        for (Integer t : type) {
            ReportResultVO report = getAutoDetectReportWithCache(apiTask.getTaskId(), t, taskReportQuery);
            if (report == null || report.report() == null) {
                continue;
            }
            try {
                LOG.info("开始给报告增加隐私水印");
                addWatermarkService.watermarkToReport(report.report(),user);
				FileUtils.copyFileToDirectory(report.report(), reportDir);
			} catch (IOException e) {
				e.getMessage();
			}
            reportResultVO = report;
        }
        
        //是否选中行为报告
        if(isCheckAction) {
        	downloadExcel(apiTask.getTaskId(), reportDir,user);
        }
        
        if (reportResultVO != null) {
            String zipName = reportZipName(reportResultVO);
            String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
            File file = new File(compress);
            if(file == null || !file.exists()){
                throw new IjiamiApplicationException("下载报告异常！");
            }
            //保存报告记录
            store = saveReportStore(file, apiTask.getCreateUserId(), apiTask.getTaskId(), paramMd5);
            if(store==null) {
                throw new IjiamiApplicationException("报告生成异常！");
            }

            map.put("bussinessId", bussinessId);
            map.put("reportName", store.getName()==null?"":store.getName());
            map.put("url", prex+store.getPath());
        }
        return map;
	}
	
	@Override
	@Async("commonExecutor")
	public void downloadAutoReportApiAsync(String bussinessId, Integer[] type, String[] itemNo, User user) throws IjiamiApplicationException {

        TReportStore store = new TReportStore();
        store.setCreateTime(new Date());
        store.setCreateUserId(user.getUserId());
        store.setBussinessId(bussinessId);
        store.setType(10); //报告生成状态
        store.setStatus(0);
        store.setTerminalType(TerminalTypeEnum.ANDROID);
        String param = bussinessId+StringUtils.join(type, ",")+(itemNo==null?"":StringUtils.join(itemNo, ","));
        String paramMd5 = Md5Utils.md5Hex(param);
        store.setParamMd5(paramMd5);
        reportStoreMapper.insertSelective(store);

        try {
			Map<String, Object> map = downloadAutoReportApi(bussinessId, type, itemNo, user);
			if(map != null) {
				store.setStatus(1);
				reportStoreMapper.updateByPrimaryKey(store);
			}
		} catch (Exception e) {
			e.getMessage();
			store.setStatus(2);
			reportStoreMapper.updateByPrimaryKey(store);
		}
	}

	private void downloadExcel(Long taskId, File reportDir,User user){
		ExcelReportQuery query = new ExcelReportQuery();
    	query.setTaskId(taskId);
    	query.setId(ConstantsUtils.ACTION_NUM_99);
        excelReportService.downloadExcelData(query,reportDir,user);
	}
	
	
	@Override
    public List<SdkVO> getAPISDKList(String documentId, Long taskId) throws IjiamiApplicationException {
        return taskService.getAPISdkList(documentId, taskId);
    }
	
	
	
	@Override
    public List<JSONObject> getDetectionSdkDetail(TTask task) {
    	List<JSONObject> jsonList = new ArrayList<JSONObject>();
    	try {
			List<SdkVO> sdkList = getAPISDKList(task.getApkDetectionDetailId(), task.getTaskId());
			
			if(sdkList == null) {
				return jsonList;
			}
			
			//查询SDK的行为个人信息相关的行为数据 TODO
			List<TPrivacyActionNougat> list =  privacyActionNougatMapper.getActionBehaviorByTaskId(task.getTaskId());
			
			sdkList.forEach(sdk->{
				JSONObject obj = new JSONObject();
				obj.put("sdkName", sdk.getName()); //SDK名称
				obj.put("packageName", sdk.getPackageName()); //SDK包名
				obj.put("manufacturer", sdk.getManufacturer()); //SDK所属厂商等相关信息
				obj.put("describe", sdk.getDescribe()); //SDK描述
				obj.put("typeName", sdk.getTypeName()); //SDK类型
				obj.put("sdkUrl", sdk.getUrl()); //连接地址
				
				obj.put("sdkPermissions", new ArrayList<JSONObject>()); //权限信息
				
				StringBuffer sf = new StringBuffer();
				List<JSONObject> actionList = new ArrayList<JSONObject>();
				obj.put("privacyActionData", actionList); //行为数据
				list.forEach(actions->{
					if(actions.getPackageName().contains(sdk.getPackageName()) && !sf.toString().contains(actions.getActionName())){
						JSONObject act = new JSONObject();
						act.put("actionId", actions.getActionId());
						act.put("actionName", actions.getActionName());
						sf.append(actions.getActionName()+",");
						actionList.add(act);
					}
				});
				obj.put("privacyActionData", actionList); //行为数据
				if(actionList != null && actionList.size()>0) {
					obj.put("sdkPermissions", setPermissions(sdk.getPermissions())); //权限信息
				}
				jsonList.add(obj);
			});
			
		} catch (IjiamiApplicationException e) {
			e.getMessage();
		}
    	
		return jsonList;
	}
    
    private static List<JSONObject> setPermissions(List<PermissionVO> pList){
    	List<JSONObject> list = new ArrayList<JSONObject>();
    	if(pList==null || pList.size()==0) {
    		return list;
    	}
    	
    	pList.forEach(p->{
    		JSONObject obj = new JSONObject();
    		obj.put("name", p.getName());
    		obj.put("aliasName", p.getRemark());
    		list.add(obj);
    	});
    	return list;
    }
    
    
    @Override
    public Map<String,Object> getBehaviorData(TTask task) {
    	Map<String, Object> map = new HashMap<>();
        //授权前行为
        map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_GRANT.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_GRANT));
        //前台行为
        map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_FRONT.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_FRONT));
        //后台行为
        map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_GROUND.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_GROUND));
        //退出行为
        map.put(String.valueOf(BehaviorStageEnum.BEHAVIOR_EXIT.getValue()), behaviorInfo(task, BehaviorStageEnum.BEHAVIOR_EXIT));
        return map;
    }

    /**
     * 保存深度检测摇一摇参数
     * @param query
     * @return
     */
    @Override
    public List<TDeepShakeValue> saveDeepShakeValue(ShakeValueQuery query) {
        List<TDeepShakeValue> result = new ArrayList<>();
        if(query.getTaskId() == null){
            return result;
        }
        List<Map<String,String>> resourceIds = query.getResourceIds();
        List<Map<String,String>> gyroscopeIds = query.getGyroscopeIds();
        if(ObjectUtils.isEmpty(resourceIds) && ObjectUtils.isEmpty(gyroscopeIds)){
            return result;
        }
        List<Map<String,String>> accelerationValue = new ArrayList<>();
        if(resourceIds != null && resourceIds.size()>0){
            resourceIds.stream().forEach(data ->{
                Map<String,String> map = new HashMap<>();
                map.put("X",data.get("acceleratX"));
                map.put("Y",data.get("acceleratY"));
                map.put("Z",data.get("acceleratZ"));
                map.put("S",data.get("acceleratS"));
                accelerationValue.add(map);
            });
        }
        List<Map<String,String>> gyroscopeValue = new ArrayList<>();
        if(gyroscopeIds != null && gyroscopeIds.size()>0 ){
            gyroscopeIds.stream().forEach(data ->{
                Map<String,String> map = new HashMap<>();
                map.put("X",data.get("gyroscopeX"));
                map.put("Y",data.get("gyroscopeY"));
                map.put("Z",data.get("gyroscopeZ"));
                map.put("maxAngle",data.get("maxAngle"));
                gyroscopeValue.add(map);
            });
        }
        TDeepShakeValue deepShakeValue = new TDeepShakeValue();
        deepShakeValue.setTaskId(Long.valueOf(query.getTaskId()));
        deepShakeValue.setCreateTime(new Date());
        if(!ObjectUtils.isEmpty(accelerationValue)){
            deepShakeValue.setAccelerationValue(JSON.toJSONString(accelerationValue));
        }
        if(!ObjectUtils.isEmpty(gyroscopeValue)){
            deepShakeValue.setGyroscopeValue(JSON.toJSONString(gyroscopeValue));
        }
        deepShakeValue.setOperaTime(Integer.valueOf(query.getOperaTime()));
        deepShakeValue.setFlag(false);
        deepShakeValue.setIsViolation(false);

        deepShakeValueMapper.insertSelective(deepShakeValue);
        result.add(deepShakeValue);
        return result;
    }


    /**
     * 查询摇一摇参数记录数据表
     * @param taskId
     * @return
     */
    @Override
    public List<TDeepShakeValue> getShakeValueData(Long taskId) {
        List<TDeepShakeValue> result = deepShakeValueMapper.getShakeValueByTaskId(taskId);
        return result;
    }

    /**
     * 根据id更新摇一摇触发跳转的状态
     * @param id
     * @param flag
     * @return
     */
    @Override
    public int updateShakeStatus(Long id, Integer flag,Integer isViolation,String description) {
        int change = 0;
        if(flag != null){
            deepShakeValueMapper.updateShakeStatusById(id,flag);
            change ++;
        }
        if(isViolation != null){
            deepShakeValueMapper.updateViolationById(id,isViolation);
            change ++;
        }
        if(StringUtils.isNotBlank(description)){
            deepShakeValueMapper.updateDescribeById(id,description);
            change ++;
        }
        return change;
    }

    @Override
    public List<THardwareSensorShakeValue> saveHardwareSensorShakeValue(HardwareSensorShakeValueQuery query) {
        // 写入
        List<THardwareSensorShakeValue> insertList = query.getSensorShakeValues()
                .stream()
                .filter(value -> Objects.isNull(value.getId()))
                .map(value -> {
            THardwareSensorShakeValue shakeValue = new THardwareSensorShakeValue();
            shakeValue.setTaskId(value.getTaskId());
            shakeValue.setTriggerValue(value.getTriggerValue());
            shakeValue.setShakePageSite(value.getShakePageSite());
            shakeValue.setPageJump(value.getPageJump());
            shakeValue.setNonCompliance(value.getNonCompliance());
            shakeValue.setCreateTime(new Date());
            return shakeValue;
        }).collect(Collectors.toList());
        if (!insertList.isEmpty()) {
            hardwareSensorShakeValueMapper.insertList(insertList);
        }
        // 更新
        List<THardwareSensorShakeValue> updateList = query.getSensorShakeValues()
                .stream()
                .filter(value -> Objects.nonNull(value.getId()))
                .map(value -> {
                    THardwareSensorShakeValue shakeValue = new THardwareSensorShakeValue();
                    shakeValue.setId(value.getId());
                    shakeValue.setTaskId(value.getTaskId());
                    shakeValue.setTriggerValue(value.getTriggerValue());
                    shakeValue.setShakePageSite(value.getShakePageSite());
                    shakeValue.setPageJump(value.getPageJump());
                    shakeValue.setNonCompliance(value.getNonCompliance());
                    return shakeValue;
                }).collect(Collectors.toList());
        for (THardwareSensorShakeValue value:updateList) {
            hardwareSensorShakeValueMapper.updateByPrimaryKeySelective(value);
        }
        List<THardwareSensorShakeValue> resultList = new ArrayList<>();
        resultList.addAll(insertList);
        resultList.addAll(updateList);
        return resultList;
    }

    @Override
    public List<THardwareSensorShakeValue> getHardwareSensorShakeValueData(Long taskId) {
        return hardwareSensorShakeValueMapper.getShakeValueByTaskId(taskId);
    }

    @Override
    public List<String> getHotfixSdk(Long taskId) {
        return taskService.getHotfixSdk(taskId);
    }

    @Override
	public JSONArray getLawStatisticalAnalysis(TTask task) {

		JSONArray array = new JSONArray();
    	Integer detectionType = task.getDetectionType();// 1快速
    	//深度检查不返回数据
    	if(detectionType == 2) {
           return array;
    	}

    	List<Map<String, Object>> lawsRole = miitDetectService.getLawList(task.getTaskId());
    	if(lawsRole == null || lawsRole.size() == 0) {
    		return array;
    	}

    	//循环法规数据
    	for (Map<String, Object> map2 : lawsRole) {
    		Long lawId = Long.parseLong(map2.get("lawId").toString());

    		JSONObject obj = new JSONObject();
    		String base = "";
    		if(map2.get("notes").toString().contains("164")){
    			base = "164";
    		}
    		if(map2.get("notes").toString().contains("191")){
    			base = "191";
    		}
    		if(map2.get("notes").toString().contains("35273")){
    			base = "35273";
    		}
    		if(map2.get("notes").toString().contains("41391")){
    			base = "41391";
    		}

    		//查询单个法规数据
    		CountLawDetectResultDTO result = miitDetectService.findLawDetectResultByTaskId(lawId, task.getTaskId());
    		CountLawDetectResultVO res = new CountLawDetectResultVO();
    		res.setComplianceNum(result.getComplianceNum());
    		res.setNonComplianceNum(result.getNonComplianceNum());
    		obj.put("lawId", base);
    		obj.put("note", map2.get("notes").toString().trim());
    		obj.put("lawData", res);

    		array.add(obj);
		}
		return array;
	}

    @Override
    public String getAppMinSdk(Long id) {
        String message = "";
        TAssets assets = assetsMapper.selectByPrimaryKey(id);
        if(assets.getMinSdkVersion() != null && assets.getMinSdkVersion() > 27){
            message = String.format("App名称：%s,minSdkVersion=%s,请选择android10手机检测",assets.getName(),assets.getMinSdkVersion());
        }
        return message;
    }

    public AutoReportUrls buildDefaultReport(long taskId, TaskReportDownLoadQuery taskReportQuery)  {
        AutoReportUrls urls = new AutoReportUrls();
        String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
        try {
            ReportResultVO report = downloadAutoDetectReportPlus(taskId, ReportTypeEnum.WORD.getValue(), taskReportQuery);
            FileVO detectReportUrl = uploadFile(report.report().getAbsolutePath());
            if (StringUtils.isNotBlank(detectReportUrl.getFileUrl())) {
                urls.setDetectReport(detectReportUrl.getFileUrl());
            }
            FileUtils.copyFileToDirectory(report.report(), reportDir);
        } catch (Exception e) {
            LOG.error("buildDefaultReport ", e);
        }
        return urls;
    }


    private TaskReportDownLoadQuery getTaskReportDownLoadQuery(long taskId, TerminalTypeEnum type) {
        List<TaskReportVO> reportVOList = taskReportService.selectReportList(type.getValue(), taskId);
        List<String> selectedItemList = new ArrayList<>();
        for (TaskReportVO reportVO:reportVOList) {
            if (CollectionUtils.isNotEmpty(reportVO.getItemList())) {
                for (TTaskReportItem item:reportVO.getItemList()) {
                    if (item.isSelected() && StringUtils.isNotBlank(item.getItemNo())) {
                        selectedItemList.add(item.getItemNo());
                    }
                }
            }
        }
        TaskReportDownLoadQuery taskReportQuery = new TaskReportDownLoadQuery();
        taskReportQuery.setTaskId(new Long[]{taskId});
        String[] itemArray = new String[selectedItemList.size()];
        selectedItemList.toArray(itemArray);
        taskReportQuery.setItemNo(itemArray);
        return taskReportQuery;
    }

    private FileVO uploadFile(String filePath) {
        FileVO fileVO = new FileVO();
        try {
            File file = new File(filePath);
            // 文件扩展名
            FileInputStream inputStream = new FileInputStream(file);

            fileVO.setFileExtName(FilenameUtils.getExtension(file.getName()));
            fileVO.setInputStream(inputStream);
            fileVO.setFileSize(file.length());
            fileVO.setFileName(file.getName());
            fileVO.setFilePath(filePath);
            singleFastDfsFileService.instance().upload(fileVO);
        } catch (Exception e) {
            LOG.error("uploadFile", e);
        }
        return fileVO;
    }
}

