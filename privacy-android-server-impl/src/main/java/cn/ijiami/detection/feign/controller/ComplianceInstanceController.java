package cn.ijiami.detection.feign.controller;


import cn.ijiami.detection.android.client.api.AndroidComplianceInstanceServiceApi;
import cn.ijiami.detection.converter.ExpertTestConverter;
import cn.ijiami.detection.server.client.base.dto.compliance.CategoryDTO;
import cn.ijiami.detection.server.client.base.dto.compliance.ComplianceActionDetailDTO;
import cn.ijiami.detection.server.client.base.dto.compliance.ComplianceActionNougatDTO;
import cn.ijiami.detection.server.client.base.dto.compliance.ComplianceNodeDTO;
import cn.ijiami.detection.server.client.base.dto.compliance.CompliancePrivacyActionNougatDTO;
import cn.ijiami.detection.server.client.base.dto.compliance.ComplianceResultDTO;
import cn.ijiami.detection.server.client.base.param.ComplianceActionParam;
import cn.ijiami.detection.server.client.base.param.ComplianceBaseParam;
import cn.ijiami.detection.service.api.compliance.IComplianceDataService;
import cn.ijiami.detection.service.api.compliance.IComplianceInstanceService;
import cn.ijiami.detection.service.api.compliance.IComplianceItemPointResultService;
import cn.ijiami.detection.service.api.compliance.IComplianceNodeService;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ComplianceInstanceController.java
 * @Description android终端专家检测Controller
 * @createTime 2025/6/25 10:50
 */
@Slf4j
@Api(value = "ComplianceInstanceController",tags ="android终端专家检测相关接口" )
@RestController
@RequestMapping("/client/")
public class ComplianceInstanceController implements AndroidComplianceInstanceServiceApi {

    @Autowired
    private IComplianceNodeService complianceNodeService;
    @Autowired
    private IComplianceInstanceService complianceInstanceService;
    @Autowired
    private IComplianceItemPointResultService complianceItemPointResultService;
    @Autowired
    private IComplianceDataService complianceDataService;

    @Override
    public List<ComplianceNodeDTO> getAllNode() {
        try{
            return ExpertTestConverter.converterToComplianceNodeDTOList(complianceNodeService.getAllNode());
        }catch (IjiamiRuntimeException e){
            log.error("获取所有节点信息失败",e);
            throw new RuntimeException("获取所有节点信息失败",e);
        }

    }

    @Override
    public List<CategoryDTO> getNodeInfo(ComplianceBaseParam param) {
        try{
            return complianceNodeService.getNodeInfo(param);
        }catch (IjiamiRuntimeException e){
            log.error("获取节点信息失败",e);
            throw new RuntimeException("获取节点信息失败",e);
        }
    }

    @Override
    public ComplianceNodeDTO getNextNode(String nodeCode) {
        try {
            return ExpertTestConverter.converterToComplianceNodeDTO(complianceNodeService.getNextNode(nodeCode));
        } catch (IjiamiRuntimeException e) {
            log.error("获取下一步节点失败",e);
            throw new RuntimeException("获取下一步节点失败",e);
        }
    }

    @Override
    public List<CategoryDTO> nextStep(ComplianceBaseParam param) {
        try {
            return complianceInstanceService.nextStep(param);
        }catch (IjiamiRuntimeException e){
            log.error("进行下一步判断失败",e);
            throw new RuntimeException("进行下一步判断失败",e);
        }
    }

    @Override
    public List<ComplianceResultDTO> batchSaveComplianceItemPointResult(ComplianceBaseParam param) {
        try {
            return complianceItemPointResultService.batchSaveComplianceItemPointResult(param);
        }catch (IjiamiRuntimeException e){
            log.error("修改或保存评估点结果失败",e);
            throw new RuntimeException("修改或保存评估点结果失败",e);
        }
    }

    @Override
    public List<ComplianceActionNougatDTO> querySensitiveBehavior(ComplianceBaseParam param) {
        try{
            return complianceDataService.querySensitiveBehavior(param);
        }catch (IjiamiRuntimeException e){
            log.error("查询个人信息统计数据结果失败",e);
            throw new RuntimeException("查询个人信息统计数据结果失败",e);
        }
    }

    @Override
    public List<CompliancePrivacyActionNougatDTO> queryMonitorActionNougat(ComplianceActionParam param) {
        try{
            return complianceDataService.queryMonitorActionNougat(param);
        }catch (IjiamiRuntimeException e){
            log.error("查询检测中行为数据失败",e);
            throw new RuntimeException("查询检测中行为数据失败",e);
        }
    }

    @Override
    public ComplianceActionDetailDTO queryMonitorActionNougatDetails(Long id) {
        try{
            return ExpertTestConverter.converToComplianceActionDetailDTO(complianceDataService.queryMonitorActionNougatDetails(id));
        }catch (IjiamiRuntimeException e){
            log.error("查看行为数据详情查询失败",e);
            throw new RuntimeException("查看行为数据详情查询失败",e);
        }
    }
}
